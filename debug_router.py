"""
Debug script để test LLM Router và tìm lỗi trong llm_usage
"""

from libs.llm_router import <PERSON><PERSON>out<PERSON>, create_router_from_env
from libs.ai import extract_sections_from_docx_with_router
import os

def debug_router_test():
    """Test router và debug các vấn đề"""
    
    print("🔍 DEBUG: Testing LLM Router")
    print("="*50)
    
    # Tạo router
    router = create_router_from_env()
    print(f"Created router with {len(router.llm_configs)} LLM configs")
    
    # Hiển thị trạng thái
    router.print_status()
    
    # Test make_request trực tiếp
    print("\n🧪 TEST: Direct router.make_request()")
    print("-"*30)
    
    test_prompt = "Hello, who are you?"
    result = router.make_request(test_prompt, input="test")
    
    if result:
        print("✅ Router request successful:")
        print(f"  - Success: {result.get('success', False)}")
        print(f"  - LLM Name: {result.get('llm_name', 'NOT_FOUND')}")
        print(f"  - Provider: {result.get('provider', 'NOT_FOUND')}")
        print(f"  - Model: {result.get('model', 'NOT_FOUND')}")
        print(f"  - All keys: {list(result.keys())}")
        
        if result.get('success'):
            print(f"  - Response preview: {result.get('response', '')[:100]}...")
        else:
            print(f"  - Error: {result.get('error', 'Unknown')}")
    else:
        print("❌ Router request failed - No result returned")
    
    # Test với file Word nếu có
    test_files = ['1.docx', '2.docx', '3.docx', 'input.docx']
    test_file = None
    
    for file in test_files:
        if os.path.exists(file):
            test_file = file
            break
    
    if test_file:
        print(f"\n📄 TEST: Processing Word file - {test_file}")
        print("-"*30)
        
        try:
            results = extract_sections_from_docx_with_router(
                word_file_path=test_file,
                max_tokens=1000,  # Nhỏ để test nhanh
                verbose=True,
                router=router
            )
            
            print(f"\n📊 FINAL RESULTS:")
            if results:
                result = results[0]
                print(f"  - Success: {result.get('success', False)}")
                print(f"  - Total chunks: {result.get('total_chunks', 0)}")
                print(f"  - Successful chunks: {result.get('successful_chunks', 0)}")
                
                llm_usage = result.get('llm_usage', {})
                print(f"\n🤖 LLM USAGE DEBUG:")
                for llm_name, usage in llm_usage.items():
                    print(f"  - {llm_name}:")
                    print(f"    * Provider: {usage.get('provider', 'NOT_FOUND')}")
                    print(f"    * Model: {usage.get('model', 'NOT_FOUND')}")
                    print(f"    * Chunks: {usage.get('chunks', 0)}")
                    print(f"    * Success: {usage.get('successful_chunks', 0)}")
                    print(f"    * Failed: {usage.get('failed_chunks', 0)}")
            else:
                print("  - No results returned")
                
        except Exception as e:
            print(f"❌ Error processing file: {e}")
    else:
        print(f"\n⚠️  No test Word files found in: {test_files}")
    
    print(f"\n🔧 FINAL ROUTER STATUS:")
    router.print_status()

if __name__ == "__main__":
    debug_router_test()
