[{"name": "OpenAI GPT-4o-mini", "provider": "openai", "model": "gpt-4o-mini", "api_base_url": "https://api.openai.com/v1", "api_token": "********************************************************", "max_calls": 50, "remaining_calls": 50, "max_input_tokens": 3500, "temperature": 0.0, "enabled": true}, {"name": "Groq Llama 3.3", "provider": "groq", "model": "llama-3.3-70b-versatile", "api_base_url": "https://api.groq.com/openai/v1", "api_token": "********************************************************", "max_calls": 100, "remaining_calls": 99, "max_input_tokens": 8000, "temperature": 0.0, "enabled": true}, {"name": "GitHub GPT-4o", "provider": "github", "model": "gpt-4o", "api_base_url": "https://models.inference.ai.azure.com", "api_token": "ghp_your-github-token-here", "max_calls": 75, "remaining_calls": 75, "max_input_tokens": 8000, "temperature": 0.0, "enabled": true}, {"name": "Gemini 1.5 Pro", "provider": "gemini", "model": "gemini-1.5-pro", "api_base_url": "", "api_token": "AIzaSyCvKbk0Y2pyVRG8FENAN1oOoIjsRdr6iKA", "max_calls": 30, "remaining_calls": 30, "max_input_tokens": 4000, "temperature": 0.0, "enabled": true}, {"name": "LocalAI Custom", "provider": "localai", "model": "custom-model", "api_base_url": "http://localhost:8080/v1", "api_token": "not-needed", "max_calls": 1000, "remaining_calls": 1000, "max_input_tokens": 6000, "temperature": 0.0, "enabled": false}]