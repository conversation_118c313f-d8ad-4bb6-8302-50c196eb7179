from langchain_community.chat_models import ChatOpenAI
from langchain.prompts import ChatPromptTemplate
from langchain.chains import <PERSON><PERSON>hain
from langchain_experimental.text_splitter import SemanticChunker
from langchain_openai import OpenAIEmbeddings
import os
import re
import docx
import sys
from dotenv import load_dotenv
from dotenv import dotenv_values

# Import common utilities
from libs.common import get_env_var

# Load environment variables from .env file
# Force reload and override existing env vars
load_dotenv(dotenv_path='.env', override=True)  
env = dotenv_values(".env")  # Also load as dictionary for easier access
#print(f"OPENAI_MODEL from env: {get_env_var('OPENAI_MODEL', 'Not found')}")

# Đọc nội dung file Word thay cho markdown

def read_docx(file_path):
    doc = docx.Document(file_path)
    full_text = []
    # Đọ<PERSON> các đoạn văn thông thường, loại bỏ dòng trống dư thừa
    for para in doc.paragraphs:
        text = para.text.strip()
        if text:
            full_text.append(text)
    # Đọc nội dung trong bảng (nếu có), mỗi dòng là 1 hàng bảng, cell cách nhau bằng tab
    for table in doc.tables:
        for row in table.rows:
            row_text = []
            for cell in row.cells:
                cell_text = cell.text.strip()
                if cell_text:
                    row_text.append(cell_text)
            if row_text:
                full_text.append('\t'.join(row_text))
    # Loại bỏ các dòng trống liên tiếp, chỉ giữ 1 dòng trống giữa các đoạn
    cleaned = []
    prev_blank = False
    for line in full_text:
        if not line.strip():
            if not prev_blank:
                cleaned.append('')
            prev_blank = True
        else:
            cleaned.append(line)
            prev_blank = False
    return '\n'.join(cleaned)

# Thay thế đường dẫn file Word tại đây
if len(sys.argv) > 1:
    word_file_path = sys.argv[1]
else:
    word_file_path = 'input.docx'  # Mặc định nếu không truyền tham số
try:
    content = read_docx(word_file_path)
except Exception as e:
    print(f"Không đọc được file Word: {word_file_path}")
    print("Lỗi chi tiết:", e)
    sys.exit(1)

# Prompt template để phân tích cấu trúc file
prompt = ChatPromptTemplate.from_template('''Bạn là một trợ lý AI. Hãy trích xuất chính xác từng phần nội dung gốc từ văn bản sau, không được cắt bỏ, rút gọn, diễn giải lại hay thay đổi bất kỳ từ nào. Chỉ phân tách và giữ nguyên bản gốc từng phần:
- Ngày tháng
- Tên hoạt động
- Mục đích
- Chuẩn bị
- Tiến hành
- Kết thúc

Nội dung:
"""
{input}
"""

Trả về kết quả dạng JSON với các trường: ngay_thang, ten_hoat_dong, muc_dich, chuan_bi, tien_hanh, ket_thuc. Mỗi trường phải giữ nguyên nội dung gốc, không được thay đổi, không được tóm tắt, không được bỏ sót bất kỳ chi tiết nào.
''')

# Khởi tạo LLMChain với OpenAI (hoặc model tương thích OpenAI API)
llm = ChatOpenAI(
    model=get_env_var('OPENAI_MODEL'),  # Use our helper function
    temperature=float(get_env_var('OPENAI_TEMPERATURE', 0.0)),
    openai_api_base=get_env_var('OPENAI_API_BASE'),  # GitHub Models API endpoint
    openai_api_key=get_env_var('OPENAI_API_KEY')
)
chain = LLMChain(llm=llm, prompt=prompt)



def count_tokens(text):
    """Ước tính số tokens (1 token ≈ 4 ký tự tiếng Anh, 1-2 ký tự tiếng Việt)"""
    return len(text) // 3  # Ước tính bảo thủ cho tiếng Việt

def ensure_token_limit(chunks, max_tokens=8000):
    """Đảm bảo không chunk nào vượt quá giới hạn tokens"""
    result_chunks = []
    for chunk in chunks:
        if count_tokens(chunk) <= max_tokens:
            result_chunks.append(chunk)
        else:
            # Chia nhỏ chunk lớn thành các phần nhỏ hơn
            lines = chunk.split('\n')
            current_chunk = ""
            
            for line in lines:
                test_chunk = current_chunk + '\n' + line if current_chunk else line
                if count_tokens(test_chunk) <= max_tokens:
                    current_chunk = test_chunk
                else:
                    if current_chunk:
                        result_chunks.append(current_chunk)
                    current_chunk = line
                    
                    # Nếu một dòng duy nhất vẫn quá dài, cắt theo ký tự
                    if count_tokens(current_chunk) > max_tokens:
                        words = current_chunk.split()
                        temp_chunk = ""
                        for word in words:
                            test_word_chunk = temp_chunk + ' ' + word if temp_chunk else word
                            if count_tokens(test_word_chunk) <= max_tokens:
                                temp_chunk = test_word_chunk
                            else:
                                if temp_chunk:
                                    result_chunks.append(temp_chunk)
                                temp_chunk = word
                        if temp_chunk:
                            current_chunk = temp_chunk
                        else:
                            current_chunk = ""
            
            if current_chunk:
                result_chunks.append(current_chunk)
    
    return result_chunks



if __name__ == '__main__':
    try:
        # Sử dụng SemanticChunker để tách văn bản dựa trên ngữ nghĩa
        embeddings = OpenAIEmbeddings(
            model=get_env_var('EMBEDDINGS_MODEL'),  # Use our helper function
            openai_api_base=get_env_var('EMBEDDINGS_API_BASE'),
            openai_api_key=get_env_var('EMBEDDINGS_API_KEY')
        )
        text_splitter = SemanticChunker(
            embeddings=embeddings,
            breakpoint_threshold_type="interquartile",  # Sử dụng interquartile để kiểm soát kích thước chunk tốt hơn
            breakpoint_threshold_amount=0.05,  # Giá trị rất thấp để tạo chunk nhỏ hơn
            number_of_chunks=None  # Không giới hạn số chunk, để tự động tách theo ngưỡng
        )
        initial_chunks = text_splitter.split_text(content)
        
        # Áp dụng giới hạn tokens cứng
        text_parts = ensure_token_limit(initial_chunks, max_tokens=7500)
        print(f"Model = {get_env_var('OPENAI_MODEL')}")
        print(f"Đã tách thành {len(text_parts)} chunks với giới hạn tokens")
        for i, part in enumerate(text_parts):
            token_count = count_tokens(part)
            print(f"Chunk {i+1}: {token_count} tokens")
        
        all_results = []
        for idx, part in enumerate(text_parts):
            print(f'--- Đang phân tích chunk {idx+1}/{len(text_parts)} ---')
            result = chain.invoke({"input": part})
            all_results.append(result['text'])
        print("\n\nKết quả từng chunk:")
        for res in all_results:
            print(res)
    except Exception as e:
        print("Lỗi khi gọi chain.invoke:", e)
