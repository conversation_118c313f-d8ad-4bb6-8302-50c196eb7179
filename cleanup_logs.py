#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script để tự động cleanup logs cũ
<PERSON><PERSON> thể chạy bằng cron job hàng ngày
"""

import os
import sys
import argparse
from datetime import datetime, timedelta
from pathlib import Path

# Add libs to path
sys.path.append('libs')

from libs.request_logger import RequestLogger


def cleanup_logs(days_to_keep=30, log_dir="logs", dry_run=False):
    """
    Cleanup logs cũ hơn số ngày chỉ định
    
    Args:
        days_to_keep (int): Số ngày giữ lại log
        log_dir (str): Th<PERSON> mục chứa logs
        dry_run (bool): Chỉ hiển thị files sẽ bị xóa, không thực sự xóa
    """
    
    print(f"🧹 Log Cleanup Script")
    print(f"📅 Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📁 Log directory: {os.path.abspath(log_dir)}")
    print(f"⏰ Keep logs for: {days_to_keep} days")
    print(f"🔍 Dry run: {'Yes' if dry_run else 'No'}")
    print("=" * 50)
    
    # Kiểm tra thư mục logs
    log_path = Path(log_dir)
    if not log_path.exists():
        print(f"❌ Log directory không tồn tại: {log_dir}")
        return False
    
    # Tính cutoff date
    cutoff_date = datetime.now() - timedelta(days=days_to_keep)
    cutoff_timestamp = cutoff_date.timestamp()
    
    print(f"🗓️  Cutoff date: {cutoff_date.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📋 Files older than this will be {'marked for deletion' if dry_run else 'deleted'}")
    print()
    
    # Tìm tất cả log files
    log_files = list(log_path.glob("api_requests_*.log"))
    
    if not log_files:
        print("ℹ️  No log files found.")
        return True
    
    print(f"📊 Found {len(log_files)} log files")
    
    # Phân loại files
    files_to_delete = []
    files_to_keep = []
    total_size_to_delete = 0
    total_size_to_keep = 0
    
    for log_file in log_files:
        try:
            stat = log_file.stat()
            file_date = datetime.fromtimestamp(stat.st_mtime)
            file_size = stat.st_size
            
            if stat.st_mtime < cutoff_timestamp:
                files_to_delete.append({
                    'file': log_file,
                    'date': file_date,
                    'size': file_size
                })
                total_size_to_delete += file_size
            else:
                files_to_keep.append({
                    'file': log_file,
                    'date': file_date,
                    'size': file_size
                })
                total_size_to_keep += file_size
                
        except Exception as e:
            print(f"⚠️  Error processing {log_file}: {e}")
    
    # Hiển thị thống kê
    print(f"📈 Summary:")
    print(f"   Files to keep: {len(files_to_keep)} ({format_size(total_size_to_keep)})")
    print(f"   Files to delete: {len(files_to_delete)} ({format_size(total_size_to_delete)})")
    print()
    
    # Hiển thị files sẽ bị xóa
    if files_to_delete:
        print(f"🗑️  Files to {'be deleted' if not dry_run else 'delete (dry run)'}:")
        for item in sorted(files_to_delete, key=lambda x: x['date']):
            print(f"   - {item['file'].name} ({item['date'].strftime('%Y-%m-%d')}, {format_size(item['size'])})")
        print()
        
        # Thực hiện xóa (nếu không phải dry run)
        if not dry_run:
            deleted_count = 0
            deleted_size = 0
            
            for item in files_to_delete:
                try:
                    item['file'].unlink()
                    deleted_count += 1
                    deleted_size += item['size']
                    print(f"✅ Deleted: {item['file'].name}")
                except Exception as e:
                    print(f"❌ Failed to delete {item['file'].name}: {e}")
            
            print()
            print(f"🎉 Cleanup completed!")
            print(f"   Deleted: {deleted_count} files ({format_size(deleted_size)})")
            print(f"   Remaining: {len(files_to_keep)} files ({format_size(total_size_to_keep)})")
        else:
            print("ℹ️  Dry run completed. No files were actually deleted.")
    else:
        print("✅ No files need to be deleted.")
    
    # Hiển thị files được giữ lại
    if files_to_keep:
        print(f"\n📋 Files to keep:")
        for item in sorted(files_to_keep, key=lambda x: x['date'], reverse=True):
            print(f"   - {item['file'].name} ({item['date'].strftime('%Y-%m-%d')}, {format_size(item['size'])})")
    
    return True


def format_size(size_bytes):
    """Format file size in human readable format"""
    if size_bytes == 0:
        return "0 B"
    
    for unit in ['B', 'KB', 'MB', 'GB']:
        if size_bytes < 1024.0:
            return f"{size_bytes:.1f} {unit}"
        size_bytes /= 1024.0
    
    return f"{size_bytes:.1f} TB"


def main():
    """Main function"""
    parser = argparse.ArgumentParser(
        description="Cleanup old API log files",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python cleanup_logs.py                    # Cleanup logs older than 30 days
  python cleanup_logs.py --days 7          # Keep only 7 days of logs
  python cleanup_logs.py --dry-run         # Show what would be deleted
  python cleanup_logs.py --days 14 --dry-run  # Dry run with 14 days retention
        """
    )
    
    parser.add_argument(
        '--days', 
        type=int, 
        default=30,
        help='Number of days to keep logs (default: 30)'
    )
    
    parser.add_argument(
        '--log-dir',
        type=str,
        default='logs',
        help='Log directory path (default: logs)'
    )
    
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Show what would be deleted without actually deleting'
    )
    
    parser.add_argument(
        '--quiet',
        action='store_true',
        help='Minimal output (for cron jobs)'
    )
    
    args = parser.parse_args()
    
    # Validate arguments
    if args.days < 1:
        print("❌ Error: --days must be at least 1")
        return 1
    
    try:
        success = cleanup_logs(
            days_to_keep=args.days,
            log_dir=args.log_dir,
            dry_run=args.dry_run
        )
        
        if success:
            if not args.quiet:
                print("\n✅ Script completed successfully!")
            return 0
        else:
            if not args.quiet:
                print("\n❌ Script failed!")
            return 1
            
    except KeyboardInterrupt:
        print("\n⚠️  Script interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
