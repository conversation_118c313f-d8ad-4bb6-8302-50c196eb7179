#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test function clean_json_response với markdown formatting
"""

import json
from ai import clean_json_response

def test_clean_markdown():
    """Test với JSON có markdown formatting"""
    
    # Test case 1: JSO<PERSON> với ```json wrapper
    markdown_json = '''```json
{
  "ngay_thang": "20-12-2023",
  "ten_hoat_dong": "Làm quà tặng chú bộ đội",
  "muc_dich": "Trẻ biết lựa chọn các nét vẽ, màu sắc phù hợp",
  "chuan_bi": "Vật liệu, giấy vẽ",
  "tien_hanh": "Hoạt động 1: Tr<PERSON> chuyện về chú bộ đội",
  "ket_thuc": "Nhận xét, chuyển hoạt động"
}
```'''
    
    # Test case 2: JSO<PERSON> với ``` wrapper (không có json)
    markdown_json2 = '''```
{
  "ngay_thang": "15-01-2024",
  "ten_hoat_dong": "Hoạt động khác"
}
```'''
    
    # Test case 3: JSON thuần túy (không có wrapper)
    plain_json = '''{
  "ngay_thang": "10-05-2024",
  "ten_hoat_dong": "JSON thuần túy"
}'''
    
    print("=== TEST CLEAN MARKDOWN JSON ===")
    
    # Test case 1
    print("\n1. Test với ```json wrapper:")
    print("Original:", repr(markdown_json[:50] + "..."))
    
    cleaned1 = clean_json_response(markdown_json)
    try:
        parsed1 = json.loads(cleaned1)
        print("✅ Parsed successfully!")
        print("Fields:", list(parsed1.keys()))
    except json.JSONDecodeError as e:
        print(f"❌ Failed: {e}")
        print("Cleaned:", repr(cleaned1[:100] + "..."))
    
    # Test case 2
    print("\n2. Test với ``` wrapper:")
    print("Original:", repr(markdown_json2[:50] + "..."))
    
    cleaned2 = clean_json_response(markdown_json2)
    try:
        parsed2 = json.loads(cleaned2)
        print("✅ Parsed successfully!")
        print("Fields:", list(parsed2.keys()))
    except json.JSONDecodeError as e:
        print(f"❌ Failed: {e}")
        print("Cleaned:", repr(cleaned2[:100] + "..."))
    
    # Test case 3
    print("\n3. Test với JSON thuần túy:")
    print("Original:", repr(plain_json[:50] + "..."))
    
    cleaned3 = clean_json_response(plain_json)
    try:
        parsed3 = json.loads(cleaned3)
        print("✅ Parsed successfully!")
        print("Fields:", list(parsed3.keys()))
    except json.JSONDecodeError as e:
        print(f"❌ Failed: {e}")
        print("Cleaned:", repr(cleaned3[:100] + "..."))

if __name__ == "__main__":
    test_clean_markdown()
