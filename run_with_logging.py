#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script để chạy server với logging và demo logging functionality
"""

import os
import sys
import time
import subprocess
import threading
from datetime import datetime


def run_server():
    """Chạy FastAPI server"""
    print("🚀 Starting FastAPI server with logging...")
    
    # Chạy server
    cmd = [sys.executable, "main.py"]
    process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True)
    
    # In output của server
    for line in iter(process.stdout.readline, ''):
        print(f"[SERVER] {line.strip()}")
        if "Application startup complete" in line or "Uvicorn running on" in line:
            print("✅ Server started successfully!")
            break
    
    return process


def demo_logging():
    """Demo logging functionality"""
    print("\n📝 Demonstrating logging functionality...")
    
    # Wait for server to be ready
    time.sleep(3)
    
    # Import test functions
    from test_logging import test_api_logging, test_log_cleanup
    
    try:
        # Run logging tests
        test_api_logging()
        
        # Show log file content
        show_log_files()
        
        # Test cleanup
        test_log_cleanup()
        
    except Exception as e:
        print(f"Error during demo: {e}")


def show_log_files():
    """Hiển thị nội dung log files"""
    print("\n📂 Log Files Content")
    print("=" * 40)
    
    import glob
    
    # Tìm tất cả log files
    log_files = glob.glob("logs/api_requests_*.log")
    
    if not log_files:
        print("No log files found.")
        return
    
    # Sort by modification time (newest first)
    log_files.sort(key=os.path.getmtime, reverse=True)
    
    for log_file in log_files[:2]:  # Show only 2 most recent files
        print(f"\n📄 File: {log_file}")
        print("-" * 30)
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
            print(f"Total lines: {len(lines)}")
            
            # Show last 5 lines
            if lines:
                print("Last 5 entries:")
                for i, line in enumerate(lines[-5:], 1):
                    print(f"  {i}. {line.strip()}")
            else:
                print("File is empty.")
                
        except Exception as e:
            print(f"Error reading file: {e}")


def main():
    """Main function"""
    print("🎯 FastAPI Logging Demo")
    print("=" * 50)
    
    # Tạo thư mục logs nếu chưa có
    os.makedirs("logs", exist_ok=True)
    
    print(f"📅 Current time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📁 Log directory: {os.path.abspath('logs')}")
    
    # Kiểm tra xem có file main.py không
    if not os.path.exists("main.py"):
        print("❌ main.py not found. Please run this script from the project root.")
        return
    
    try:
        # Chạy server trong thread riêng
        server_thread = threading.Thread(target=run_server, daemon=True)
        server_thread.start()
        
        # Wait for server to start
        print("⏳ Waiting for server to start...")
        time.sleep(5)
        
        # Demo logging
        demo_logging()
        
        print("\n🎉 Demo completed!")
        print("\n📋 Summary:")
        print("- API requests are now logged to daily files in logs/ directory")
        print("- Log files are named: api_requests_YYYY-MM-DD.log")
        print("- Each request and response is logged with timestamp and details")
        print("- Use /logs endpoint to view logs via API")
        print("- Use /logs/dates to see available log dates")
        print("- Use /logs/cleanup to clean old logs")
        
        print(f"\n🔗 API Documentation: http://localhost:8000/docs")
        print(f"🔗 Log API: http://localhost:8000/logs")
        print(f"🔗 Log Dates: http://localhost:8000/logs/dates")
        
        # Keep server running
        print("\n⚡ Server is running. Press Ctrl+C to stop.")
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n👋 Stopping server...")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    except KeyboardInterrupt:
        print("\n👋 Demo interrupted.")


if __name__ == "__main__":
    main()
