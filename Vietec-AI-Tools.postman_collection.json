{"info": {"_postman_id": "63e6d1e8-e8bd-4448-b8cb-3ecb409e2dc7", "name": "Vietec-AI-Tools", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "4915550"}, "item": [{"name": "Question & Answer Search", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "project", "value": "qlcl", "description": "pms_din<PERSON><PERSON><PERSON>, pms_th<PERSON>, pms_cando, qlcl, khgd", "type": "text"}, {"key": "text", "value": "Công nhận trường đạt chuẩn quốc gia ?", "type": "text"}]}, "url": {"raw": "{{API_URL}}/qa/search", "host": ["{{API_URL}}"], "path": ["qa", "search"]}}, "response": []}, {"name": "Extract Content From Docx", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": "/Volumes/TUANNA/vietec/github/ai-test/6.docx"}]}, "url": {"raw": "{{API_URL}}/khgd/extract-content-from-docx", "host": ["{{API_URL}}"], "path": ["khgd", "extract-content-from-docx"]}}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}], "variable": [{"key": "API_URL", "value": "http://*************:8801", "type": "string"}]}