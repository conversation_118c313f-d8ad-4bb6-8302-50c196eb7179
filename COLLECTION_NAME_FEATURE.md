# Tính năng Collection Name trong TypesenseVectorDB

## Tổng quan

Tính năng mới cho phép truyền `collection_name` vào các function `search_and_answer` và `search_similar_documents`, gi<PERSON><PERSON> bạn có thể tìm kiếm trong các collection khác nhau mà không cần tạo instance TypesenseVectorDB mới.

## Các function được cập nhật

### 1. `search_similar_documents`

```python
def search_similar_documents(self, query: str, limit: int = 10, threshold: float = 0.7, collection_name: Optional[str] = None) -> Dict[str, Any]:
```

**Tham số mới:**
- `collection_name` (Optional[str]): Tên collection để tìm kiếm. Nếu `None`, sẽ sử dụng collection mặc định.

### 2. `search_and_answer`

```python
def search_and_answer(self, question: str, limit: int = 5, threshold: float = 0.7, collection_name: Optional[str] = None) -> Dict[str, Any]:
```

**Tham số mới:**
- `collection_name` (Optional[str]): Tên collection để tìm kiếm. Nếu `None`, sẽ sử dụng collection mặc định.

## Cách sử dụng

### Ví dụ 1: Sử dụng collection mặc định

```python
from libs.typesense_vector_db import TypesenseVectorDB

# Khởi tạo với collection mặc định
db = TypesenseVectorDB(collection_name="default_collection")

# Tìm kiếm trong collection mặc định
result = db.search_and_answer(
    question="Cân đối thực đơn tự động thế nào?",
    limit=3,
    threshold=0.5
)
```

### Ví dụ 2: Truyền collection_name cụ thể

```python
from libs.typesense_vector_db import TypesenseVectorDB

# Khởi tạo với collection mặc định
db = TypesenseVectorDB(collection_name="default_collection")

# Tìm kiếm trong collection cụ thể
result = db.search_and_answer(
    question="Cân đối thực đơn tự động thế nào?",
    limit=3,
    threshold=0.5,
    collection_name="test_documents"  # Tìm kiếm trong collection này
)
```

### Ví dụ 3: Tìm kiếm documents tương tự

```python
# Tìm kiếm trong collection mặc định
result1 = db.search_similar_documents(
    query="hoạt động giáo dục",
    limit=5,
    threshold=0.6
)

# Tìm kiếm trong collection cụ thể
result2 = db.search_similar_documents(
    query="hoạt động giáo dục",
    limit=5,
    threshold=0.6,
    collection_name="education_docs"
)
```

## Lợi ích

1. **Linh hoạt**: Có thể tìm kiếm trong nhiều collection khác nhau với cùng một instance TypesenseVectorDB.

2. **Hiệu quả**: Không cần tạo nhiều instance TypesenseVectorDB cho các collection khác nhau.

3. **Tương thích ngược**: Code cũ vẫn hoạt động bình thường vì `collection_name` là tham số tùy chọn.

4. **Dễ sử dụng**: Chỉ cần thêm tham số `collection_name` khi cần thiết.

## Lưu ý

- Nếu `collection_name` là `None` hoặc không được truyền, function sẽ sử dụng collection mặc định (`self.collection_name`).
- Collection được chỉ định phải tồn tại trong Typesense, nếu không sẽ có lỗi.
- Tất cả các collection phải có cùng schema để function hoạt động đúng.

## Demo

Chạy file `demo_collection_name.py` để xem demo chi tiết:

```bash
python demo_collection_name.py
```

## Test

File `qa_test.py` đã được cập nhật để test tính năng mới. Uncomment các dòng test để chạy:

```python
# Test với collection mặc định
result = db.search_and_answer(
    question=question,
    limit=3,
    threshold=0.5
)

# Test với collection cụ thể
result_specific = db.search_and_answer(
    question=question,
    limit=3,
    threshold=0.5,
    collection_name="test_documents"
)
```

## Kết luận

Tính năng này giúp TypesenseVectorDB trở nên linh hoạt hơn trong việc quản lý và tìm kiếm dữ liệu từ nhiều collection khác nhau, đồng thời vẫn duy trì tính đơn giản và dễ sử dụng.
