# LLM Router với MySQL Database Integration

## Tổng quan

Hệ thống LLM Router đã được nâng cấp để tích hợp với MySQL database, cung cấp các tính năng:

- **Config Management**: Ưu tiên load cấu hình LLM từ database thay vì file JSON
- **Logging requests**: <PERSON><PERSON><PERSON> chi tiết mỗi request/response vào database
- **Daily call tracking**: <PERSON><PERSON><PERSON><PERSON> lý remaining_calls theo ngày, tự động reset hàng ngày
- **Statistics**: Thống kê chi tiết về usage, performance, success rate
- **Data persistence**: D<PERSON> liệu được lưu trữ bền vững thay vì chỉ trong memory
- **Fallback mechanism**: Tự động fallback về file JSON khi database không khả dụng

## Cài đặt

### 1. Cài đặt dependencies

```bash
pip install -r requirements.txt
```

### 2. Thiết lập MySQL Database

Chạy script setup tự động:

```bash
python setup_database.py
```

Hoặc setup thủ công:

1. Tạo database:
```sql
CREATE DATABASE llm_router CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. Tạo user (tùy chọn):
```sql
CREATE USER 'llm_user'@'%' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON llm_router.* TO 'llm_user'@'%';
FLUSH PRIVILEGES;
```

3. Cấu hình trong file `.env`:
```env
DB_HOST=localhost
DB_PORT=3306
DB_NAME=llm_router
DB_USER=llm_user
DB_PASSWORD=your_password
USE_DATABASE=true
```

### 3. Khởi tạo database schema

```bash
python daily_reset.py sync
```

## Cấu trúc Database

### Bảng `request_logs`
Lưu chi tiết mỗi request:
- `timestamp`: Thời gian request
- `llm_name`: LLM được sử dụng
- `prompt_text`: Prompt gửi đi
- `response_text`: Response nhận về
- `input_tokens`, `output_tokens`: Số tokens
- `success`: Request có thành công không
- `processing_time_ms`: Thời gian xử lý (ms)

### Bảng `daily_calls`
Quản lý calls theo ngày:
- `date`: Ngày
- `llm_name`: Tên LLM
- `max_calls`: Số calls tối đa trong ngày
- `used_calls`: Số calls đã sử dụng
- `remaining_calls`: Số calls còn lại

### Bảng `llm_configs`
Lưu cấu hình LLM:
- `name`: Tên LLM
- `provider`: Provider (openai, gemini, ...)
- `model`: Model name
- `max_calls`: Giới hạn calls
- `enabled`: Có enable không

## Sử dụng

### Basic Usage

```python
from llm_router import LLMRouter

# Tạo router với database
router = LLMRouter(use_database=True)

# Request sẽ tự động được log vào database
result = router.make_request("Hello, who are you?")

# remaining_calls được quản lý từ database
print(f"Remaining calls: {router.get_remaining_calls_from_database('LLM Name')}")
```

### API Endpoints

#### Lấy thống kê database
```bash
GET /database-stats?days=7
```

#### Reset daily calls
```bash
POST /database-reset
```

#### Cleanup old logs
```bash
POST /database-cleanup?days_to_keep=30
```

### Command Line Tools

#### Reset daily calls
```bash
python daily_reset.py reset
```

#### Xem thống kê
```bash
python daily_reset.py stats 7
```

#### Test database connection
```bash
python daily_reset.py test
```

#### Đồng bộ configs từ JSON
```bash
python daily_reset.py sync
```

## Daily Reset Automation

Để tự động reset calls hàng ngày, thêm vào crontab:

```bash
# Reset vào 00:05 hàng ngày
5 0 * * * cd /path/to/ai-test && python daily_reset.py reset
```

Hoặc sử dụng systemd timer:

```ini
# /etc/systemd/system/llm-router-reset.service
[Unit]
Description=LLM Router Daily Reset
After=network.target

[Service]
Type=oneshot
User=your-user
WorkingDirectory=/path/to/ai-test
ExecStart=/usr/bin/python3 daily_reset.py reset

# /etc/systemd/system/llm-router-reset.timer
[Unit]
Description=Run LLM Router Reset Daily
Requires=llm-router-reset.service

[Timer]
OnCalendar=*-*-* 00:05:00
Persistent=true

[Install]
WantedBy=timers.target
```

Kích hoạt:
```bash
sudo systemctl enable llm-router-reset.timer
sudo systemctl start llm-router-reset.timer
```

## Thống kê và Monitoring

### Thống kê tổng quan
```python
router = LLMRouter(use_database=True)
stats = router.get_database_stats(days=7)

print(f"Total requests: {stats['general']['total_requests']}")
print(f"Success rate: {stats['general']['successful_requests'] / stats['general']['total_requests'] * 100}%")
```

### Thống kê theo LLM
```python
for llm_stat in stats['by_llm']:
    print(f"{llm_stat['llm_name']}: {llm_stat['requests']} requests")
```

### Thống kê theo ngày
```python
for day_stat in stats['by_day']:
    print(f"{day_stat['date']}: {day_stat['requests']} requests")
```

## Migration từ File-based

Nếu bạn đang sử dụng hệ thống cũ (chỉ JSON file):

1. **Backup dữ liệu cũ:**
```bash
cp llm_config.json llm_config.json.backup
```

2. **Setup database:**
```bash
python setup_database.py
```

3. **Đồng bộ configs:**
```bash
python daily_reset.py sync
```

4. **Test:**
```bash
python daily_reset.py test
```

Hệ thống sẽ tự động fallback về file JSON nếu database không khả dụng.

## Troubleshooting

### Database connection errors
1. Kiểm tra MySQL đã chạy:
```bash
sudo systemctl status mysql
```

2. Test connection:
```bash
mysql -h localhost -u llm_user -p llm_router
```

3. Kiểm tra .env file:
```bash
cat .env | grep DB_
```

### Permission errors
```sql
GRANT ALL PRIVILEGES ON llm_router.* TO 'llm_user'@'%';
FLUSH PRIVILEGES;
```

### Schema errors
```bash
python daily_reset.py sync
```

### Performance issues
1. **Tạo indexes:**
```sql
USE llm_router;
CREATE INDEX idx_request_logs_date ON request_logs(date);
CREATE INDEX idx_daily_calls_date ON daily_calls(date);
```

2. **Cleanup old data:**
```bash
python daily_reset.py cleanup 30
```

## Configuration Options

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `DB_HOST` | MySQL host | `localhost` |
| `DB_PORT` | MySQL port | `3306` |
| `DB_NAME` | Database name | `llm_router` |
| `DB_USER` | Database user | `root` |
| `DB_PASSWORD` | Database password | `` |
| `USE_DATABASE` | Enable database | `true` |
| `CLEANUP_OLD_LOGS_DAYS` | Days to keep logs | `30` |

### Router Options

```python
# Sử dụng database
router = LLMRouter(use_database=True)

# Không sử dụng database (fallback to JSON)
router = LLMRouter(use_database=False)
```

## Security

1. **Database credentials**: Không commit file `.env` vào git
2. **Database user**: Tạo user riêng với quyền hạn chế
3. **Network**: Cấu hình firewall cho MySQL port
4. **Backup**: Backup database định kỳ:

```bash
mysqldump -u llm_user -p llm_router > backup_$(date +%Y%m%d).sql
```

## Performance Tips

1. **Indexing**: Database đã có indexes cơ bản, có thể thêm theo nhu cầu
2. **Partitioning**: Với dữ liệu lớn, có thể partition theo tháng
3. **Archiving**: Tự động archive data cũ
4. **Connection pooling**: Sử dụng connection pool cho production

## Support

Để báo cáo bug hoặc request feature, tạo issue trong repository.

## Cấu hình Text Length Limits

Mặc định, `response_text` và `prompt_text` được lưu đầy đủ vào database (không giới hạn). Bạn có thể cấu hình giới hạn độ dài để tiết kiệm dung lượng:

### Environment Variables
```env
# Không giới hạn (mặc định)
MAX_PROMPT_LENGTH=
MAX_RESPONSE_LENGTH=

# Có giới hạn
MAX_PROMPT_LENGTH=5000
MAX_RESPONSE_LENGTH=50000
```

### Programmatic Configuration
```python
# Không giới hạn
router = LLMRouter(use_database=True)

# Có giới hạn
router = LLMRouter(
    use_database=True,
    max_prompt_length=5000,
    max_response_length=50000
)

# Từ environment
router = create_router_from_env()
```

### Database Schema Support
Database schema hỗ trợ:
- `prompt_text`: TEXT (65,535 characters)
- `response_text`: LONGTEXT (4,294,967,295 characters ~ 4GB)

Điều này có nghĩa bạn có thể lưu response rất dài mà không lo bị cắt ngắn.

## Config Management Priority

### Thứ tự ưu tiên load configs

Khi khởi tạo LLMRouter với database enabled, hệ thống sẽ load configs theo thứ tự ưu tiên:

1. **Database First**: Ưu tiên load từ database
2. **JSON Fallback**: Nếu database trống hoặc lỗi, fallback về file JSON
3. **Auto Sync**: Khi load từ JSON, tự động sync vào database

```python
# Database được ưu tiên
router = LLMRouter(
    config_file="llm_config.json",
    use_database=True,
    db_config=database_config
)

# Configs được load theo thứ tự:
# 1. Thử load từ database
# 2. Nếu database trống → load từ JSON và sync vào database
# 3. Nếu database lỗi → load từ JSON (fallback)
```

### Save Config Behavior

Khi lưu configs, hệ thống sẽ:

1. **Primary**: Lưu vào database (nếu available)
2. **Backup**: Luôn backup vào file JSON

```python
# Save ưu tiên database
router.save_config()

# Thực hiện:
# 1. Lưu vào database (priority)
# 2. Backup vào JSON file
# 3. Log kết quả
```

### Migration từ JSON sang Database

Khi chuyển từ hệ thống cũ (chỉ JSON) sang database:

```python
# Bước 1: Enable database
router = LLMRouter(
    config_file="existing_config.json",
    use_database=True,
    db_config=db_config
)

# Bước 2: Configs từ JSON sẽ tự động sync vào database
# Bước 3: Từ lần khởi tạo tiếp theo, configs sẽ load từ database
```

### Demo Scripts

#### Test Database Priority
```bash
python test_database_priority.py
```

#### Demo Config Loading
```bash
python demo_database_config_priority.py
```
