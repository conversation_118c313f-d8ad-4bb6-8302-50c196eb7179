#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Demo sử dụng tính năng collection_name trong search_and_answer
"""

from libs.typesense_vector_db import TypesenseVectorDB

def demo_collection_name_usage():
    """Demo cách sử dụng collection_name trong search_and_answer"""
    
    print("="*80)
    print(" DEMO SỬ DỤNG COLLECTION_NAME TRONG SEARCH_AND_ANSWER ")
    print("="*80)
    
    # Khởi tạo TypesenseVectorDB với collection mặc định
    db = TypesenseVectorDB(collection_name="default_collection")
    
    question = "Cân đối thực đơn tự động thế nào?"
    
    print(f"\n❓ Câu hỏi: {question}")
    
    # Cách 1: Sử dụng collection mặc định (không truyền collection_name)
    print("\n" + "="*60)
    print("🔹 CÁCH 1: Sử dụng collection mặc định")
    print("="*60)
    
    result1 = db.search_and_answer(
        question=question,
        limit=3,
        threshold=0.5
    )
    
    print_result(result1, "Collection mặc định")
    
    # Cách 2: Truyền collection_name cụ thể
    print("\n" + "="*60)
    print("🔹 CÁCH 2: Truyền collection_name cụ thể")
    print("="*60)
    
    result2 = db.search_and_answer(
        question=question,
        limit=3,
        threshold=0.5,
        collection_name="test_documents"  # Truyền collection name cụ thể
    )
    
    print_result(result2, "Collection cụ thể 'test_documents'")
    
    # Cách 3: Truyền collection_name khác
    print("\n" + "="*60)
    print("🔹 CÁCH 3: Truyền collection_name khác")
    print("="*60)
    
    result3 = db.search_and_answer(
        question=question,
        limit=3,
        threshold=0.5,
        collection_name="another_collection"  # Collection khác
    )
    
    print_result(result3, "Collection khác 'another_collection'")

def print_result(result, title):
    """In kết quả một cách đẹp mắt"""
    print(f"\n📊 {title}:")
    
    if result["success"]:
        print(f"✅ Trả lời thành công!")
        print(f"🤖 LLM sử dụng: {result.get('llm_used', 'Unknown')}")
        print(f"📊 Confidence: {result.get('confidence', 0):.3f}")
        print(f"📚 Số documents tham khảo: {result.get('total_documents_found', 0)}")
        print(f"\n💬 Câu trả lời:")
        print(result["answer"])
        
        if result.get("sources"):
            print(f"\n📖 Nguồn tham khảo:")
            for i, source in enumerate(result["sources"]):
                print(f"   {i+1}. {source['source_file']} (similarity: {source['similarity']:.3f})")
    else:
        print(f"❌ Lỗi: {result['error']}")

def demo_search_similar_documents():
    """Demo cách sử dụng collection_name trong search_similar_documents"""
    
    print("\n" + "="*80)
    print(" DEMO SỬ DỤNG COLLECTION_NAME TRONG SEARCH_SIMILAR_DOCUMENTS ")
    print("="*80)
    
    db = TypesenseVectorDB(collection_name="default_collection")
    
    query = "hoạt động giáo dục"
    
    print(f"\n🔍 Query: {query}")
    
    # Tìm kiếm trong collection mặc định
    print("\n🔹 Tìm kiếm trong collection mặc định:")
    result1 = db.search_similar_documents(
        query=query,
        limit=3,
        threshold=0.5
    )
    
    print_search_result(result1, "Collection mặc định")
    
    # Tìm kiếm trong collection cụ thể
    print("\n🔹 Tìm kiếm trong collection cụ thể:")
    result2 = db.search_similar_documents(
        query=query,
        limit=3,
        threshold=0.5,
        collection_name="test_documents"
    )
    
    print_search_result(result2, "Collection 'test_documents'")

def print_search_result(result, title):
    """In kết quả tìm kiếm"""
    print(f"\n📊 {title}:")
    
    if result["success"]:
        print(f"✅ Tìm thấy {result['total_found']} documents")
        
        for i, doc in enumerate(result["documents"]):
            print(f"\n📄 Document {i+1}:")
            print(f"   - File: {doc['source_file']}")
            print(f"   - Similarity: {doc['similarity']:.3f}")
            print(f"   - Content: {doc['content'][:100]}...")
    else:
        print(f"❌ Lỗi: {result['error']}")

if __name__ == "__main__":
    try:
        demo_collection_name_usage()
        demo_search_similar_documents()
        
        print("\n" + "="*80)
        print("✅ HOÀN THÀNH DEMO!")
        print("="*80)
        
    except Exception as e:
        print(f"❌ Lỗi trong demo: {e}")
