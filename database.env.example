# Database Configuration for LLM Router
# Copy this file to .env and update with your MySQL credentials

# MySQL Database Settings
DB_HOST=localhost
DB_PORT=3306
DB_NAME=llm_router
DB_USER=root
DB_PASSWORD=

# Database Options
DB_CHARSET=utf8mb4

# LLM API Keys (optional, để override configs)
OPENAI_API_KEY=your-openai-api-key
GROQ_API_KEY=your-groq-api-key
GEMINI_API_KEY=your-gemini-api-key
GITHUB_TOKEN=your-github-token

# Application Settings
USE_DATABASE=true
LOG_LEVEL=INFO
CLEANUP_OLD_LOGS_DAYS=30

# Text Length Limits (leave empty for no limits)
MAX_PROMPT_LENGTH=
MAX_RESPONSE_LENGTH=
