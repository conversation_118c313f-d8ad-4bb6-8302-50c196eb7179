from langchain.text_splitter import MarkdownHeaderTextSplitter
from langchain.schema import Document

# Đọc nội dung file 1.md
with open('1.md', 'r', encoding='utf-8') as f:
    md_content = f.read()

# Đ<PERSON>nh nghĩa các header markdown để phân tích cấu trúc
headers_to_split_on = [
    ("#", "header1"),
    ("##", "header2"),
    ("###", "header3"),
    ("**", "bold_section"),
    ("-", "list_item")
]

# Khởi tạo splitter
splitter = MarkdownHeaderTextSplitter(headers_to_split_on=headers_to_split_on)

# Phân tích cấu trúc file markdown
splits = splitter.split_text(md_content)

# In ra cấu trúc các phần đã tách
for i, doc in enumerate(splits):
    print(f"--- Section {i+1} ---")
    print(f"Metadata: {doc.metadata}")
    print(f"Content:\n{doc.page_content}\n")
