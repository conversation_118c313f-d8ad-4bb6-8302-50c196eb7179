# Collection Validation cho Excel Import

## 🎯 Tổng quan

Chức năng này cho phép kiểm tra tính hợp lệ của collection trong MySQL trước khi import dữ liệu từ Excel vào Typesense. Điều này đảm bảo rằng chỉ những collection đã được định nghĩa và phê duyệt mới được phép import dữ liệu.

## 🏗️ Cấu trúc

### 1. Bảng Collections trong MySQL

```sql
CREATE TABLE collections (
    id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    name VARCHAR(255) COLLATE utf8mb4_unicode_ci NOT NULL,
    display_name VARCHAR(255) COLLATE utf8mb4_unicode_ci NOT NULL,
    description TEXT COLLATE utf8mb4_unicode_ci,
    is_active TINYINT(1) NOT NULL DEFAULT '1',
    created_at TIMESTAMP NULL DEFAULT NULL,
    updated_at TIMESTAMP NULL DEFAULT NULL,
    PRIMARY KEY (id),
    UNIQUE KEY collections_name_unique (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 2. Model Collection

- **File**: `libs/collection_model.py`
- **Chức năng**: CRUD operations cho bảng collections
- **Methods chính**:
  - `create(name, display_name, description)`: Tạo collection mới
  - `exists(name)`: Kiểm tra collection có tồn tại
  - `get_by_name(name)`: Lấy thông tin collection
  - `get_all(active_only)`: Lấy tất cả collections
  - `update(name, display_name, description, is_active)`: Cập nhật collection
  - `delete(name, soft_delete)`: Xóa collection
  - `get_stats()`: Lấy thống kê collections

### 3. Cập nhật TypesenseVectorDB

- **File**: `libs/typesense_vector_db.py`
- **Method**: `import_excel_to_typesense()`
- **Tham số mới**: `collection_name` (optional)
- **Logic**: Kiểm tra collection trong MySQL trước khi import

## 🚀 Cài đặt và Sử dụng

### 1. Khởi tạo Database và Collections

```bash
# Chạy script setup để tạo database và collections mẫu
python setup_collections.py
```

Script này sẽ:
- Tạo bảng `collections` trong MySQL
- Tạo các collections mẫu
- Cung cấp giao diện quản lý collections tương tác

### 2. Sử dụng trong Code

#### Import Excel với Collection Validation

```python
from libs.typesense_vector_db import TypesenseVectorDB

# Khởi tạo database
db = TypesenseVectorDB(collection_name="my_typesense_collection")

# Import với collection validation
result = db.import_excel_to_typesense(
    file_path="data.xlsx",
    title="My Data",
    metadata={"category": "education"},
    collection_name="education_qa"  # Kiểm tra collection này trong MySQL
)

if result["success"]:
    print(f"✅ Import thành công: {result['imported_documents']} documents")
    print(f"Collection verified: {result['collection_verified']}")
else:
    print(f"❌ Import thất bại: {result['error']}")
```

#### Import Excel không có Validation

```python
# Import mà không kiểm tra collection (như trước đây)
result = db.import_excel_to_typesense(
    file_path="data.xlsx",
    title="My Data"
    # Không có collection_name -> không validation
)
```

### 3. Quản lý Collections

#### Sử dụng Collection Model

```python
from libs.collection_model import Collection

collection_model = Collection()

# Tạo collection mới
collection_model.create("my_collection", "My Collection", "Mô tả collection")

# Kiểm tra collection tồn tại
if collection_model.exists("my_collection"):
    print("Collection tồn tại")

# Lấy thông tin collection
info = collection_model.get_by_name("my_collection")
print(f"Collection: {info['name']} ({info['display_name']}) - {info['description']}")

# Lấy tất cả collections
collections = collection_model.get_all()
for collection in collections:
    print(f"- {collection['name']} ({collection['display_name']})")

# Cập nhật collection
collection_model.update("my_collection", display_name="Updated Collection", description="Mô tả mới")

# Xóa collection (soft delete)
collection_model.delete("my_collection", soft_delete=True)
```

## 🧪 Testing

### 1. Chạy Demo

```bash
# Demo đầy đủ với tất cả tính năng
python demo_collection_validation.py

# Demo import Excel với collection validation
python demo_excel_import.py
```

### 2. Chạy Unit Tests

```bash
# Chạy tất cả unit tests
python test_collection_validation.py
```

Tests bao gồm:
- Test Collection Model CRUD operations
- Test Excel import với collection validation
- Test database integration

### 3. Test Cases

#### Test 1: Import với Collection hợp lệ
- **Input**: Excel file + collection_name tồn tại trong MySQL
- **Expected**: Import thành công, `collection_verified = True`

#### Test 2: Import với Collection không hợp lệ
- **Input**: Excel file + collection_name không tồn tại trong MySQL
- **Expected**: Import thất bại với thông báo lỗi rõ ràng

#### Test 3: Import không có Collection validation
- **Input**: Excel file mà không có collection_name
- **Expected**: Import thành công như trước đây, `collection_verified = False`

## 📋 Collections Mẫu

Script setup sẽ tạo các collections mẫu sau:

| Collection Name | Mô tả |
|----------------|-------|
| `education_qa` | Collection cho dữ liệu Q&A giáo dục |
| `system_help` | Collection cho hệ thống trợ giúp |
| `user_manual` | Collection cho hướng dẫn người dùng |
| `nutrition_data` | Collection cho dữ liệu dinh dưỡng |
| `admin_guide` | Collection cho hướng dẫn quản trị |
| `faq_general` | Collection cho câu hỏi thường gặp |

## 🔧 Cấu hình

### Database Configuration

Đảm bảo file `.env` có cấu hình database đúng:

```env
DB_HOST=localhost
DB_PORT=3306
DB_NAME=your_database
DB_USER=your_username
DB_PASSWORD=your_password
DB_CHARSET=utf8mb4
```

### Typesense Configuration

```env
TYPESENSE_HOST=localhost
TYPESENSE_PORT=8108
TYPESENSE_PROTOCOL=http
TYPESENSE_API_KEY=your_api_key
```

## 🚨 Xử lý Lỗi

### Lỗi thường gặp:

1. **Collection không tồn tại**
   - **Lỗi**: `Collection 'xxx' không tồn tại trong cơ sở dữ liệu MySQL`
   - **Giải pháp**: Tạo collection bằng Collection Model

2. **Không kết nối được MySQL**
   - **Lỗi**: Không thể kiểm tra collection trong MySQL
   - **Giải pháp**: Kiểm tra cấu hình database trong `.env`

3. **Không kết nối được Typesense**
   - **Lỗi**: Lỗi import vào Typesense
   - **Giải pháp**: Kiểm tra Typesense server và cấu hình

## 📈 Lợi ích

1. **Bảo mật**: Chỉ cho phép import vào collections đã được phê duyệt
2. **Quản lý tập trung**: Tất cả collections được quản lý trong MySQL
3. **Tương thích ngược**: Code cũ vẫn hoạt động bình thường
4. **Linh hoạt**: Có thể bật/tắt validation theo nhu cầu
5. **Audit trail**: Theo dõi được collections nào đã được tạo và sử dụng

## 🔄 Workflow

```mermaid
graph TD
    A[Bắt đầu Import Excel] --> B{Có collection_name?}
    B -->|Không| C[Import trực tiếp vào Typesense]
    B -->|Có| D[Kiểm tra collection trong MySQL]
    D --> E{Collection tồn tại?}
    E -->|Không| F[Trả về lỗi]
    E -->|Có| G[Import vào Typesense]
    C --> H[Hoàn thành]
    G --> H
    F --> I[Dừng]
```

## 📞 Hỗ trợ

Nếu gặp vấn đề, hãy:
1. Kiểm tra logs trong console
2. Chạy `python setup_collections.py` để kiểm tra database
3. Chạy unit tests để xác định vấn đề
4. Kiểm tra cấu hình trong file `.env`
