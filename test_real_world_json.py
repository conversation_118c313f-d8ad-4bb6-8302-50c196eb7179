#!/usr/bin/env python3
"""
Test thực tế với trường hợp phức tạp của việc x<PERSON> lý JSON từ AI
"""

import json
from ai import clean_json_response

def test_real_world_scenario():
    """Test với trường hợp thực tế phức tạp"""
    print("=== Test Real World Scenario ===")
    
    # Mô phỏng response từ AI với nhiều hoạt động trong một chunk
    ai_response = '''
    {
        "ngay_thang": "15-03-2024",
        "ten_hoat_dong": "Tập thể dục buổi sáng",
        "muc_dich": "<PERSON><PERSON><PERSON> luyện sức khỏe cho học sinh",
        "chuan_bi": "<PERSON><PERSON> tập, dụng cụ thể thao",
        "tien_hanh": "Tập thể dục 30 phút, chạy bộ 15 phút"
    }
    
    {
        "ngay_thang": "16-03-2024", 
        "ten_hoat_dong": "<PERSON>ọ<PERSON> bài mô<PERSON>",
        "muc_dich": "<PERSON><PERSON>ng cố kiến thức về phép nhân",
        "chuan_bi": "Bảng, phấn, sách giáo khoa",
        "tien_hanh": "Giảng bài 25 phút, làm bài tập 20 phút"
    }
    
    {
        "ngay_thang": "17-03-2024",
        "ten_hoat_dong": "Hoạt động ngoại khóa",
        "muc_dich": "Phát triển kỹ năng xã hội",
        "chuan_bi": "Đồ chơi, giấy bút",
        "tien_hanh": "Chơi trò chơi nhóm, vẽ tranh"
    }
    '''
    
    print("Original AI Response:")
    print(ai_response)
    print("\n" + "="*50 + "\n")
    
    # Xử lý bằng hàm clean_json_response
    cleaned = clean_json_response(ai_response)
    print("After clean_json_response:")
    print(cleaned)
    print("\n" + "="*50 + "\n")
    
    # Áp dụng logic mới để kiểm tra array
    if not cleaned.strip().startswith('['):
        final_json = "[" + cleaned + "]"
    else:
        final_json = cleaned
    
    print("Final JSON (with array check):")
    print(final_json)
    print("\n" + "="*50 + "\n")
    
    # Test parse
    try:
        parsed = json.loads(final_json)
        print(f"✅ Parse thành công!")
        print(f"Type: {type(parsed)}")
        print(f"Số items: {len(parsed) if isinstance(parsed, list) else 1}")
        
        if isinstance(parsed, list):
            for i, item in enumerate(parsed, 1):
                print(f"\n--- Item {i} ---")
                for key, value in item.items():
                    print(f"{key}: {value}")
    except json.JSONDecodeError as e:
        print(f"❌ Lỗi parse JSON: {e}")

def test_edge_cases():
    """Test các trường hợp edge cases"""
    print("\n\n=== Test Edge Cases ===")
    
    # Case 1: JSON đã có dấu ngoặc vuông
    print("\n--- Case 1: Already has brackets ---")
    already_array = '[{"ngay_thang": "01-01-2024", "ten_hoat_dong": "Test"}]'
    cleaned = clean_json_response(already_array)
    
    if not cleaned.strip().startswith('['):
        final = "[" + cleaned + "]"
    else:
        final = cleaned
    
    print(f"Input: {already_array}")
    print(f"After processing: {final}")
    
    # Case 2: Empty response
    print("\n--- Case 2: Empty response ---")
    empty = ""
    cleaned_empty = clean_json_response(empty)
    
    if not cleaned_empty.strip().startswith('['):
        final_empty = "[" + cleaned_empty + "]"
    else:
        final_empty = cleaned_empty
    
    print(f"Input: '{empty}'")
    print(f"After processing: '{final_empty}'")
    
    # Case 3: Malformed JSON
    print("\n--- Case 3: Malformed JSON ---")
    malformed = '{"ngay_thang": "01-01-2024", "ten_hoat_dong": "Test" // missing closing brace'
    cleaned_malformed = clean_json_response(malformed)
    
    if not cleaned_malformed.strip().startswith('['):
        final_malformed = "[" + cleaned_malformed + "]"
    else:
        final_malformed = cleaned_malformed
    
    print(f"Input: {malformed}")
    print(f"After processing: {final_malformed}")
    
    try:
        json.loads(final_malformed)
        print("✅ Parse thành công")
    except json.JSONDecodeError as e:
        print(f"❌ Parse failed (expected): {e}")

if __name__ == "__main__":
    print("🧪 Testing Real World JSON Scenarios...\n")
    
    test_real_world_scenario()
    test_edge_cases()
    
    print("\n✅ All tests completed!")
