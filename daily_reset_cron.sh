#!/bin/bash
# Cron job template để chạy daily reset cho LLM Router
# Copy file này và cập nhật đường dẫn phù hợp

# Đường dẫn đến project
PROJECT_PATH="/Volumes/TUANNA/vietec/github/ai-test"

# Đường dẫn đến Python
PYTHON_PATH="/usr/bin/python3"

# Log file
LOG_FILE="$PROJECT_PATH/daily_reset.log"

# CD vào project directory
cd "$PROJECT_PATH"

# Chạy daily reset
echo "$(date): Starting daily reset..." >> "$LOG_FILE"
"$PYTHON_PATH" daily_reset.py reset >> "$LOG_FILE" 2>&1

# Check exit code
if [ $? -eq 0 ]; then
    echo "$(date): Daily reset completed successfully" >> "$LOG_FILE"
else
    echo "$(date): Daily reset failed" >> "$LOG_FILE"
fi

# Cleanup logs c<PERSON> hơn 30 ngày (optional)
# "$PYTHON_PATH" daily_reset.py cleanup 30 >> "$LOG_FILE" 2>&1

echo "$(date): Daily reset job finished" >> "$LOG_FILE"

# Để thêm vào crontab:
# crontab -e
# Thêm dòng sau để chạy lúc 00:05 hàng ngày:
# 5 0 * * * /path/to/this/script.sh
