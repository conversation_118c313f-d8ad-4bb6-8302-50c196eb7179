"""
Example sử dụng LLM Router để xử lý file Word

Hướng dẫn sử dụng:
1. Cập nhật API tokens trong file llm_config.json
2. Chạy script với các tùy chọn khác nhau
"""

from llm_router import LLMRouter
from ai import main_with_router

def setup_example_router():
    """Tạo router với cấu hình ví dụ"""

    # Tạo router (chỉ sử dụng database)
    router = LLMRouter()
    
    # Router sẽ load configs từ database
    print(f"✅ Đã tạo router với {len(router.llm_configs)} LLM configs từ database")
    return router

def demo_router_usage():
    """Demo cách sử dụng router"""
    
    print("🚀 DEMO LLM ROUTER")
    print("="*50)
    
    # 1. Tạo router
    router = setup_example_router()
    
    # 2. Hiển thị trạng thái
    print("\n1. TRẠNG THÁI BAN ĐẦU:")
    router.print_status()
    
    # 3. Test random selection
    print("\n2. TEST RANDOM SELECTION:")
    for i in range(5):
        selected = router.select_random_llm()
        if selected:
            print(f"   Lần {i+1}: {selected.name} (còn {selected.remaining_calls} lượt)")
        else:
            print(f"   Lần {i+1}: Không có LLM khả dụng")
    
    # 4. Demo enable/disable
    print("\n3. TEST ENABLE/DISABLE:")
    router.disable_llm("Gemini 1.5 Pro")
    router.enable_llm("LocalAI Custom")
    
    available = router.get_available_llms()
    print(f"   LLM khả dụng sau khi enable/disable: {len(available)}")
    for llm in available:
        print(f"   - {llm.name}")
    
    # 5. Test making requests (commented out để tránh gọi API thật)
    print("\n4. SIMULATION REQUESTS:")
    print("   (Giả lập 10 requests)")
    
    for i in range(10):
        selected = router.select_random_llm()
        if selected:
            # Simulate request
            with router.lock:
                selected.remaining_calls -= 1
                router.save_config()
            print(f"   Request {i+1}: {selected.name} (còn {selected.remaining_calls} lượt)")
        else:
            print(f"   Request {i+1}: Không có LLM khả dụng")
    
    # 6. Trạng thái cuối
    print("\n5. TRẠNG THÁI SAU KHI SIMULATION:")
    router.print_status()
    
    # 7. Reset
    print("\n6. RESET VÀ TRẠNG THÁI CUỐI:")
    router.reset_all_calls()
    router.print_status()

def demo_with_word_file():
    """Demo xử lý file Word với router (cần file Word thật)"""
    
    print("\n" + "="*60)
    print("🤖 DEMO XỬ LÝ FILE WORD VỚI ROUTER")
    print("="*60)
    
    # Kiểm tra có file Word không
    test_files = ['input.docx', '1.docx', '2.docx', '3.docx']
    word_file = None
    
    for file in test_files:
        try:
            with open(file, 'rb'):
                word_file = file
                break
        except FileNotFoundError:
            continue
    
    if not word_file:
        print("❌ Không tìm thấy file Word nào để test")
        print("💡 Tạo file Word tên 'input.docx' để test")
        return
    
    print(f"📄 Sử dụng file: {word_file}")
    
    # Tạo router
    router = setup_example_router()
    
    # API keys được quản lý trong database
    print("ℹ️  API keys được quản lý trong database. Sử dụng database management tools để cập nhật.")
    
    # Kiểm tra có LLM nào khả dụng không
    available = router.get_available_llms()
    if not available:
        print("❌ Không có LLM nào khả dụng (chưa có API keys hoặc hết lượt call)")
        print("💡 Cập nhật API keys và configs trong database")
        return
    
    print(f"✅ Có {len(available)} LLM khả dụng")
    
    # Xử lý file Word
    try:
        main_with_router(word_file, router)
    except Exception as e:
        print(f"❌ Lỗi khi xử lý file Word: {e}")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == '--demo':
            demo_router_usage()
        elif sys.argv[1] == '--word':
            demo_with_word_file()
        elif sys.argv[1] == '--both':
            demo_router_usage()
            demo_with_word_file()
        else:
            print("Usage:")
            print("  python example_router.py --demo     # Demo router basic")
            print("  python example_router.py --word     # Demo với file Word")
            print("  python example_router.py --both     # Demo cả hai")
    else:
        print("🚀 LLM ROUTER EXAMPLE")
        print("=" * 30)
        print("Chọn demo:")
        print("1. Demo router basic")
        print("2. Demo với file Word") 
        print("3. Demo cả hai")
        
        choice = input("\nNhập lựa chọn (1-3): ").strip()
        
        if choice == '1':
            demo_router_usage()
        elif choice == '2':
            demo_with_word_file()
        elif choice == '3':
            demo_router_usage()
            demo_with_word_file()
        else:
            print("❌ Lựa chọn không hợp lệ")
