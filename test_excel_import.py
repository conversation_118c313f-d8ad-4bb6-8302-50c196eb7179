#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test file cho chức năng import Excel vào Typesense
Kiểm tra import dữ liệu từ file Excel với cấu trúc: <PERSON><PERSON><PERSON> năng, <PERSON><PERSON><PERSON> hỏi, <PERSON><PERSON><PERSON> án
"""

import os
import sys
import json
import pandas as pd
from typing import Dict, Any
from libs.typesense_vector_db import TypesenseVectorDB

def print_separator(title: str):
    """In dòng phân cách với tiêu đề"""
    print("\n" + "="*80)
    print(f" {title} ")
    print("="*80)

def print_result(result: Dict[str, Any], title: str = "Kết quả"):
    """In kết quả một cách đẹp mắt"""
    print(f"\n📊 {title}:")
    print(json.dumps(result, ensure_ascii=False, indent=2))

def create_sample_excel():
    """Tạo file Excel mẫu để test"""
    print_separator("TẠO FILE EXCEL MẪU")
    
    # Dữ liệu mẫu dựa trên hình ảnh bạn cung cấp
    sample_data = [
        {
            "Chức năng": "Câu hình",
            "Câu hỏi": "Muốn thêm/sửa/xóa nhóm trẻ thì làm như nào?",
            "Đáp án": "Để thực hiện, vui lòng thực hiện theo các bước sau:\n1. Vào \"Cấu hình\"\n2. Chọn \"Nhóm trẻ\" để thêm/sửa xóa\n3. Bấm \"Lưu\""
        },
        {
            "Chức năng": "Câu hình", 
            "Câu hỏi": "Thay đổi logic chính sửa tiền dịch vụ",
            "Đáp án": "Để thực hiện, vui lòng thực hiện theo các bước sau:\n1. Vào \"Cấu hình\"\n2. Chọn \"Dịch vụ\" để thêm/sửa xóa\n3. Bấm \"Lưu\"\n(Lưu ý: Nếu trước đây đã có không nên chỉnh sửa mà phải vào cài đặt chính tây trước nếu có sự thay đổi)"
        },
        {
            "Chức năng": "Câu hình",
            "Câu hỏi": "Thay đổi tiền bữa ăn",
            "Đáp án": "Để thực hiện, vui lòng thực hiện theo các bước sau:\n1. Vào \"Cấu hình\"\n2. Chọn \"Thông tin thực đơn\" nhập tiền cần thay đổi\n3. Bấm \"Lưu\"\n(Lưu ý: không nên đặt tiền bữa theo tên khác hoàn toàn vì dữ bữa sáng-> bữa trưa)"
        },
        {
            "Chức năng": "Câu hình",
            "Câu hỏi": "Thay đổi thư ký bữa ăn",
            "Đáp án": "Để thực hiện, vui lòng thực hiện theo các bước sau:\n1. Vào \"Cấu hình\"\n2. Chọn \"Thông tin thực đơn\" chọn thư ký cần thay đổi\n3. Bấm \"Lưu\""
        },
        {
            "Chức năng": "Câu hình",
            "Câu hỏi": "Thay đổi định mức dinh dưỡng",
            "Đáp án": "Để thực hiện, vui lòng thực hiện theo các bước sau:\n1. Vào \"Cấu hình\"\n2. Chọn \"Định mức dinh dưỡng\" thực hiện thay đổi theo nhu cầu của trường\n3. Bấm \"Lưu\""
        },
        {
            "Chức năng": "Câu hình",
            "Câu hỏi": "Thay đổi ký tự phân cách hàng nghìn của số lượng và thành tiền",
            "Đáp án": "Để thực hiện, vui lòng thực hiện theo các bước sau:\n1. Vào \"Cấu hình\"\n2. Chọn \"Cấu hình chung\" chọn ký tự phân cách hàng nghìn\n3. Bấm \"Lưu\""
        }
    ]
    
    # Tạo DataFrame
    df = pd.DataFrame(sample_data)
    
    # Lưu file Excel
    excel_file = "sample_qa_data.xlsx"
    df.to_excel(excel_file, index=False, engine='openpyxl')
    
    print(f"✅ Đã tạo file Excel mẫu: {excel_file}")
    print(f"📊 File chứa {len(df)} dòng dữ liệu")
    
    # Hiển thị preview
    print("\n📋 Preview dữ liệu:")
    for i, row in df.head(3).iterrows():
        print(f"\nDòng {i+1}:")
        print(f"  - Chức năng: {row['Chức năng']}")
        print(f"  - Câu hỏi: {row['Câu hỏi'][:50]}...")
        print(f"  - Đáp án: {row['Đáp án'][:50]}...")
    
    return excel_file

def test_connection():
    """Test kết nối Typesense"""
    print_separator("TEST KẾT NỐI TYPESENSE")
    
    try:
        # Khởi tạo TypesenseVectorDB
        db = TypesenseVectorDB(collection_name="qa_excel_test")
        
        # Lấy thống kê collection
        stats = db.get_collection_stats()
        
        if stats["success"]:
            print("✅ Kết nối Typesense thành công!")
            print(f"📊 Collection: {stats['collection_name']}")
            print(f"📄 Tổng documents: {stats['total_documents']}")
            return db
        else:
            print("❌ Lỗi kết nối Typesense!")
            return None
            
    except Exception as e:
        print(f"❌ Lỗi kết nối: {e}")
        return None

def test_import_excel(db: TypesenseVectorDB, excel_file: str):
    """Test import file Excel"""
    print_separator("TEST IMPORT FILE EXCEL")
    
    print(f"🧪 Test import file: {excel_file}")
    
    result = db.import_excel_to_typesense(
        file_path=excel_file,
        title="Dữ liệu Q&A từ Excel",
        metadata={
            "test_run": True,
            "imported_by": "test_excel_import.py",
            "data_source": "excel"
        }
    )
    
    print_result(result, "Kết quả Import Excel")
    
    if result["success"]:
        print("✅ Import Excel thành công!")
        return True
    else:
        print("❌ Import Excel thất bại!")
        return False

def test_search_qa(db: TypesenseVectorDB):
    """Test tìm kiếm Q&A"""
    print_separator("TEST TÌM KIẾM Q&A")
    
    # Các câu hỏi test
    test_queries = [
        "cấu hình nhóm trẻ",
        "thay đổi tiền bữa ăn",
        "định mức dinh dưỡng",
        "phân cách hàng nghìn",
        "dịch vụ"
    ]
    
    for query in test_queries:
        print(f"\n🔍 Tìm kiếm: '{query}'")
        
        result = db.search_similar_documents(
            query=query,
            limit=3,
            threshold=0.3
        )
        
        if result["success"]:
            print(f"✅ Tìm thấy {result['total_found']} documents")
            
            for i, doc in enumerate(result["documents"]):
                metadata = json.loads(doc.get('metadata_json', '{}'))
                print(f"\n📄 Document {i+1}:")
                print(f"   - Chức năng: {metadata.get('chuc_nang', 'N/A')}")
                print(f"   - Câu hỏi: {metadata.get('cau_hoi', 'N/A')[:50]}...")
                print(f"   - Similarity: {doc['similarity']:.3f}")
        else:
            print(f"❌ Lỗi tìm kiếm: {result['error']}")

def test_qa_system(db: TypesenseVectorDB):
    """Test hệ thống hỏi đáp"""
    print_separator("TEST HỆ THỐNG HỎI ĐÁP")

    # Các câu hỏi test
    test_questions = [
        "Làm thế nào để thêm nhóm trẻ mới?",
        "Tôi muốn thay đổi giá tiền bữa ăn thì làm sao?",
        "Cách cấu hình định mức dinh dưỡng như thế nào?"
    ]

    for question in test_questions:
        print(f"\n❓ Câu hỏi: {question}")

        result = db.search_and_answer(
            question=question,
            limit=3,
            threshold=0.3
        )

        if result["success"]:
            print(f"✅ Trả lời thành công!")
            print(f"🤖 LLM sử dụng: {result.get('llm_used', 'Unknown')}")
            print(f"📊 Confidence: {result.get('confidence', 0):.3f}")
            print(f"📚 Số documents tham khảo: {result.get('total_documents_found', 0)}")
            print(f"\n💬 Câu trả lời:")
            print(result["answer"])

            if result.get("sources"):
                print(f"\n📖 Nguồn tham khảo:")
                for i, source in enumerate(result["sources"]):
                    metadata = json.loads(source.get('metadata_json', '{}'))
                    print(f"   {i+1}. Chức năng: {metadata.get('chuc_nang', 'N/A')} (similarity: {source['similarity']:.3f})")
        else:
            print(f"❌ Lỗi trả lời: {result['error']}")

def main():
    """Hàm chính để chạy tất cả tests"""
    print_separator("TYPESENSE EXCEL IMPORT - TEST")
    print("🧪 Bắt đầu test chức năng import Excel vào Typesense")
    
    # Bước 1: Tạo file Excel mẫu
    excel_file = create_sample_excel()
    
    # Bước 2: Test kết nối
    db = test_connection()
    if not db:
        print("❌ Không thể kết nối Typesense. Dừng test.")
        return
    
    # Bước 3: Test import Excel
    import_success = test_import_excel(db, excel_file)
    
    if import_success:
        # Bước 4: Test tìm kiếm
        test_search_qa(db)
        
        # Bước 5: Test hệ thống Q&A
        test_qa_system(db)
    else:
        print("❌ Import thất bại. Bỏ qua các test khác.")
    
    print_separator("KẾT THÚC TEST")
    print("✅ Hoàn thành test import Excel!")
    
    # Cleanup
    if os.path.exists(excel_file):
        print(f"🗑️  Xóa file test: {excel_file}")
        os.remove(excel_file)

if __name__ == "__main__":
    main()
