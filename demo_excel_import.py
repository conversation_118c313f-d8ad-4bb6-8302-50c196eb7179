#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Demo đơn giản cho chức năng import Excel vào Typesense
"""

import os
import json
import pandas as pd
from libs.typesense_vector_db import TypesenseVectorDB

def main():
    print("=" * 60)
    print(" DEMO IMPORT EXCEL VÀO TYPESENSE ")
    print("=" * 60)
    
    # Kiểm tra file Excel
    excel_files = [f for f in os.listdir('.') if f.endswith(('.xlsx', '.xls'))]
    
    if not excel_files:
        print("❌ Không tìm thấy file Excel nào trong thư mục hiện tại")
        
        # Tạo file Excel mẫu
        print("\n🔧 Tạo file Excel mẫu...")
        sample_data = [
            {
                "Chức năng": "Cấu hình",
                "Câu hỏi": "Muốn thêm/sửa/xóa nhóm trẻ thì làm như nào?",
                "Đáp án": "<PERSON><PERSON> thực hiện, vui lòng thực hiện theo các bước sau:\n1. Vào \"Cấu hình\"\n2. Chọn \"Nhóm trẻ\" để thêm/sửa xóa\n3. Bấm \"Lưu\""
            },
            {
                "Chức năng": "Cấu hình", 
                "Câu hỏi": "Thay đổi logic chính sửa tiền dịch vụ",
                "Đáp án": "Để thực hiện, vui lòng thực hiện theo các bước sau:\n1. Vào \"Cấu hình\"\n2. Chọn \"Dịch vụ\" để thêm/sửa xóa\n3. Bấm \"Lưu\"\n(Lưu ý: Nếu trước đây đã có không nên chỉnh sửa mà phải vào cài đặt chính tây trước nếu có sự thay đổi)"
            },
            {
                "Chức năng": "Cấu hình",
                "Câu hỏi": "Thay đổi tiền bữa ăn",
                "Đáp án": "Để thực hiện, vui lòng thực hiện theo các bước sau:\n1. Vào \"Cấu hình\"\n2. Chọn \"Thông tin thực đơn\" nhập tiền cần thay đổi\n3. Bấm \"Lưu\"\n(Lưu ý: không nên đặt tiền bữa theo tên khác hoàn toàn vì dữ bữa sáng-> bữa trưa)"
            },
            {
                "Chức năng": "Cấu hình",
                "Câu hỏi": "Thay đổi thư ký bữa ăn",
                "Đáp án": "Để thực hiện, vui lòng thực hiện theo các bước sau:\n1. Vào \"Cấu hình\"\n2. Chọn \"Thông tin thực đơn\" chọn thư ký cần thay đổi\n3. Bấm \"Lưu\""
            },
            {
                "Chức năng": "Cấu hình",
                "Câu hỏi": "Thay đổi định mức dinh dưỡng",
                "Đáp án": "Để thực hiện, vui lòng thực hiện theo các bước sau:\n1. Vào \"Cấu hình\"\n2. Chọn \"Định mức dinh dưỡng\" thực hiện thay đổi theo nhu cầu của trường\n3. Bấm \"Lưu\""
            }
        ]
        
        df = pd.DataFrame(sample_data)
        excel_file = "demo_qa_data.xlsx"
        df.to_excel(excel_file, index=False, engine='openpyxl')
        excel_files = [excel_file]
        print(f"✅ Đã tạo file Excel mẫu: {excel_file}")
    
    # Hiển thị danh sách file Excel
    print(f"\n📁 File Excel có sẵn:")
    for i, file in enumerate(excel_files):
        print(f"   {i+1}. {file}")
    
    # Chọn file để import
    if len(excel_files) == 1:
        selected_file = excel_files[0]
        print(f"\n📄 Sử dụng file: {selected_file}")
    else:
        try:
            choice = int(input(f"\nChọn file (1-{len(excel_files)}): ")) - 1
            selected_file = excel_files[choice]
        except (ValueError, IndexError):
            print("❌ Lựa chọn không hợp lệ")
            return
    
    # Nhập tên collection
    collection_name = input("\nNhập tên collection (để trống = 'qa_demo'): ").strip()
    if not collection_name:
        collection_name = "qa_demo"
    
    try:
        # Khởi tạo database
        print(f"\n🔗 Kết nối Typesense với collection: {collection_name}")
        db = TypesenseVectorDB(collection_name=collection_name)
        
        # Import Excel
        print(f"\n📊 Import file Excel: {selected_file}")
        result = db.import_excel_to_typesense(
            file_path=selected_file,
            title=f"Demo Q&A Data - {selected_file}",
            metadata={
                "demo": True,
                "imported_by": "demo_excel_import.py"
            }
        )
        
        # Hiển thị kết quả
        print("\n" + "="*50)
        print(" KẾT QUẢ IMPORT ")
        print("="*50)
        
        if result["success"]:
            print("✅ Import thành công!")
            print(f"📊 Tổng số dòng: {result['total_rows']}")
            print(f"📄 Documents đã import: {result['imported_documents']}")
            print(f"⏭️ Bỏ qua trùng lặp: {result['skipped_duplicates']}")
            
            # Test tìm kiếm nhanh
            print("\n🔍 Test tìm kiếm nhanh:")
            test_queries = ["cấu hình nhóm trẻ", "tiền bữa ăn", "định mức dinh dưỡng"]
            
            for query in test_queries:
                search_result = db.search_similar_documents(
                    query=query,
                    limit=2,
                    threshold=0.3
                )
                
                if search_result["success"] and search_result["total_found"] > 0:
                    print(f"\n   🔹 '{query}': Tìm thấy {search_result['total_found']} kết quả")
                    for doc in search_result["documents"][:1]:  # Chỉ hiển thị 1 kết quả
                        metadata = json.loads(doc.get('metadata_json', '{}'))
                        print(f"      - {metadata.get('chuc_nang', 'N/A')}: {metadata.get('cau_hoi', 'N/A')[:50]}...")
                else:
                    print(f"\n   🔹 '{query}': Không tìm thấy kết quả")
            
            # Test Q&A
            print("\n🤖 Test Q&A:")
            test_question = "Làm thế nào để thêm nhóm trẻ mới?"
            qa_result = db.search_and_answer(
                question=test_question,
                limit=3,
                threshold=0.3
            )
            
            if qa_result["success"]:
                print(f"❓ Câu hỏi: {test_question}")
                print(f"💬 Trả lời: {qa_result['answer'][:200]}...")
                print(f"📊 Confidence: {qa_result.get('confidence', 0):.3f}")
            else:
                print(f"❌ Lỗi Q&A: {qa_result['error']}")
                
        else:
            print("❌ Import thất bại!")
            print(f"🔥 Lỗi: {result['error']}")
        
    except Exception as e:
        print(f"❌ Lỗi: {e}")
    
    print("\n" + "="*60)
    print("✅ Demo hoàn thành!")
    print("📖 Xem thêm hướng dẫn trong README_EXCEL_IMPORT.md")
    print("="*60)

if __name__ == "__main__":
    main()
