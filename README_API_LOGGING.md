# API Request Logging System

Hệ thống ghi log các request vào API một cách tự động và chi tiết.

## 📋 Tổng quan

Hệ thống logging này sẽ tự động ghi lại tất cả các request và response của API vào các file log theo ngày. Mỗi ngày sẽ có một file log riêng biệt trong thư mục `logs/`.

## 🚀 Tính năng

### ✅ Tự động ghi log
- **Request logging**: Ghi lại method, URL, headers, query parameters, body, client IP, user agent
- **Response logging**: Ghi lại status code, response time, response size, error (nếu có)
- **Daily files**: Mỗi ngày một file log riêng biệt
- **JSON format**: Dữ liệu log được lưu dưới dạng JSON để dễ dàng parse và phân tích

### 🔒 Bảo mật
- **Sensitive headers filtering**: Tự động loại bỏ các headers nhạy cảm như `authorization`, `cookie`
- **Body size limiting**: Giới hạn kích thước body được log (10KB), body lớn hơn sẽ được truncate
- **Error handling**: Xử lý lỗi gracefully, không ảnh hưởng đến API chính

### 📊 Quản lý log
- **View logs**: API endpoints để xem logs theo ngày
- **List dates**: Xem danh sách các ngày có log
- **Cleanup**: Tự động xóa logs cũ

## 📁 Cấu trúc file log

### Tên file
```
logs/api_requests_YYYY-MM-DD.log
```

Ví dụ: `logs/api_requests_2025-07-07.log`

### Format log entry

#### Request log:
```
HH:MM:SS | INFO | REQUEST | {JSON_DATA}
```

#### Response log:
```
HH:MM:SS | LEVEL | RESPONSE | {JSON_DATA}
```

Trong đó `LEVEL` có thể là:
- `INFO`: Status code 200-399
- `WARNING`: Status code 400-499  
- `ERROR`: Status code 500+

### Ví dụ log entry

#### Request:
```json
{
  "timestamp": "2025-07-07T13:36:38.521627",
  "method": "POST",
  "url": "http://localhost:8000/qa/search",
  "client_ip": "127.0.0.1",
  "user_agent": "python-requests/2.32.4",
  "query_params": {},
  "headers": {
    "host": "localhost:8000",
    "user-agent": "python-requests/2.32.4",
    "content-type": "application/x-www-form-urlencoded"
  },
  "body": "project=test_project&text=what+is+AI?"
}
```

#### Response:
```json
{
  "timestamp": "2025-07-07T13:36:38.522323",
  "method": "POST", 
  "url": "http://localhost:8000/qa/search",
  "status_code": 404,
  "response_time": 0.058,
  "response_size": 0,
  "error": "Collection not found"
}
```

## 🔧 API Endpoints

### 1. Xem logs theo ngày
```http
GET /logs?date=YYYY-MM-DD&limit=100
```

**Parameters:**
- `date` (optional): Ngày cần xem log, mặc định là hôm nay
- `limit` (optional): Số dòng log tối đa, mặc định là 100

**Response:**
```json
{
  "success": true,
  "message": "Logs cho ngày 2025-07-07",
  "date": "2025-07-07",
  "logs": ["log_entry_1", "log_entry_2", ...],
  "total": 50,
  "total_in_file": 127
}
```

### 2. Danh sách ngày có log
```http
GET /logs/dates
```

**Response:**
```json
{
  "success": true,
  "message": "Tìm thấy 5 ngày có log",
  "dates": [
    {
      "date": "2025-07-07",
      "file_size": 5783,
      "modified": "2025-07-07T13:38:33.594973"
    }
  ],
  "total": 5
}
```

### 3. Xóa logs cũ
```http
POST /logs/cleanup?days_to_keep=30
```

**Parameters:**
- `days_to_keep`: Số ngày giữ lại log (mặc định 30 ngày)

**Response:**
```json
{
  "success": true,
  "message": "Đã xóa logs cũ hơn 30 ngày",
  "days_to_keep": 30
}
```

## 🧪 Testing

### 1. Test standalone logging
```bash
python test_logger_standalone.py
```

### 2. Test với API server
```bash
# Terminal 1: Chạy server
python main.py

# Terminal 2: Test logging
python test_logging.py
```

### 3. Demo đầy đủ
```bash
python run_with_logging.py
```

## 📊 Monitoring và Analytics

### Thông tin được ghi log

#### Request Information:
- **Timestamp**: Thời gian request
- **Method**: HTTP method (GET, POST, PUT, DELETE, etc.)
- **URL**: Full URL được request
- **Client IP**: IP address của client
- **User Agent**: Browser/client information
- **Query Parameters**: URL parameters
- **Headers**: HTTP headers (filtered)
- **Body**: Request body (limited size)

#### Response Information:
- **Timestamp**: Thời gian response
- **Status Code**: HTTP status code
- **Response Time**: Thời gian xử lý (seconds)
- **Response Size**: Kích thước response (bytes)
- **Error**: Thông tin lỗi (nếu có)

### Performance Metrics
- **X-Process-Time header**: Mỗi response sẽ có header này chứa thời gian xử lý

## 🔧 Configuration

### Environment Variables
Không cần cấu hình thêm, hệ thống sử dụng cấu hình mặc định:
- **Log directory**: `logs/`
- **Max body size**: 10KB
- **Default cleanup**: 30 ngày

### Customization
Để tùy chỉnh, chỉnh sửa file `libs/request_logger.py`:
- Thay đổi thư mục log
- Điều chỉnh kích thước body tối đa
- Thêm/bớt headers được filter
- Tùy chỉnh format log

## 🚨 Lưu ý quan trọng

1. **Disk Space**: Log files có thể trở nên lớn, cần monitor disk space
2. **Performance**: Logging có thể ảnh hưởng nhẹ đến performance
3. **Privacy**: Cần cẩn thận với sensitive data trong logs
4. **Rotation**: Sử dụng cleanup API để quản lý logs cũ

## 📈 Best Practices

1. **Regular cleanup**: Chạy cleanup định kỳ để tránh đầy disk
2. **Log analysis**: Sử dụng tools như `jq` để phân tích JSON logs
3. **Monitoring**: Monitor log file sizes và disk usage
4. **Backup**: Backup logs quan trọng trước khi cleanup

## 🔍 Log Analysis Examples

### Tìm tất cả requests lỗi 500:
```bash
grep "ERROR.*RESPONSE" logs/api_requests_2025-07-07.log
```

### Đếm số requests theo method:
```bash
grep "REQUEST" logs/api_requests_2025-07-07.log | jq -r '.method' | sort | uniq -c
```

### Tìm requests chậm nhất:
```bash
grep "RESPONSE" logs/api_requests_2025-07-07.log | jq -r '.response_time' | sort -nr | head -10
```

## 🎯 Kết luận

Hệ thống logging này cung cấp khả năng theo dõi và phân tích chi tiết các request đến API, giúp:
- Debug và troubleshoot issues
- Monitor performance
- Analyze usage patterns
- Security auditing
- Compliance requirements
