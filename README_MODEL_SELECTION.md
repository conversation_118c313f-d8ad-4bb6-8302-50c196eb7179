# Word Document Processor với Model Selection

## Tính năng mới: <PERSON><PERSON><PERSON>ọn Model AI

Bây giờ bạn có thể chọn model AI để xử lý tài liệu Word từ các nhà cung cấp khác nhau.

## Các Model được hỗ trợ

### GITHUB Models
- `openai/gpt-4.1` - GPT-4.1 model từ GitHub
- `openai/gpt-4.1-mini` - GPT-4.1 Mini model từ GitHub

### GROQ Models  
- `llama-3.3-70b-versatile` - Llama 3.3 70B model từ Groq
- `meta-llama/llama-4-maverick-17b-128e-instruct` - Llama 4 Maverick model từ Groq

## Cấu hình Environment Variables

Cập nhật file `.env` với các thông tin sau:

```env
# GitHub API Configuration
GITHUB_OPENAI_API_KEY=your_github_token
GITHUB_OPENAI_API_BASE=https://models.inference.ai.azure.com

# Groq API Configuration  
GROQ_API_KEY=your_groq_api_key
GROQ_API_BASE=https://api.groq.com/openai/v1

# Model Lists
GITHUB_MODELS=openai/gpt-4.1,openai/gpt-4.1-mini
GROQ_MODELS=llama-3.3-70b-versatile,meta-llama/llama-4-maverick-17b-128e-instruct
```

## Sử dung trong Code

### 1. Sử dụng function trực tiếp

```python
from ai import extract_sections_from_docx

# Sử dụng model mặc định
result = extract_sections_from_docx("document.docx")

# Chọn model GROQ
result = extract_sections_from_docx(
    "document.docx", 
    model="llama-3.3-70b-versatile"
)

# Chọn model GitHub
result = extract_sections_from_docx(
    "document.docx",
    model="openai/gpt-4.1"
)
```

### 2. Sử dụng API Endpoints

#### Lấy danh sách models khả dụng:
```bash
GET /models
```

#### Upload và xử lý file với model:
```bash
POST /process-docx
Form Data:
- file: document.docx
- model: llama-3.3-70b-versatile (optional)
- max_tokens: 3500 (optional)
- verbose: true (optional)
```

#### Xử lý file từ đường dẫn với model:
```bash
POST /process-docx-path  
Form Data:
- file_path: /path/to/document.docx
- model: openai/gpt-4.1 (optional)
- max_tokens: 3500 (optional)
- verbose: true (optional)
```

## Ví dụ sử dụng API

### Lấy danh sách models:
```bash
curl -X GET "http://localhost:8000/models"
```

### Xử lý với model cụ thể:
```bash
curl -X POST "http://localhost:8000/process-docx-path" \
  -F "file_path=document.docx" \
  -F "model=llama-3.3-70b-versatile" \
  -F "verbose=true"
```

## Chạy test

```bash
# Test model selection functionality
python model_selection_example.py

# Test API endpoints
python test_model_selection.py
```

## Lưu ý

1. **API Keys**: Đảm bảo bạn có API keys hợp lệ cho từng nhà cung cấp
2. **Model Validation**: API sẽ kiểm tra tính hợp lệ của model trước khi xử lý
3. **Backward Compatibility**: Nếu không chọn model, hệ thống sẽ sử dụng cấu hình mặc định
4. **Error Handling**: API sẽ trả về lỗi rõ ràng nếu model không được hỗ trợ

## Troubleshooting

### Model không được hỗ trợ
- Kiểm tra tên model có chính xác không
- Gọi `/models` endpoint để xem danh sách models khả dụng

### API Key errors
- Kiểm tra API keys trong file `.env`
- Đảm bảo API keys có quyền truy cập models tương ứng

### Connection errors
- Kiểm tra URL endpoints trong file `.env`
- Kiểm tra kết nối internet
