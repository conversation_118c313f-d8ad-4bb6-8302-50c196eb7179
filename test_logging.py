#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script để kiểm tra logging system
"""

import requests
import json
import time
from datetime import datetime


def test_api_logging():
    """Test logging system bằng cách gọi các API endpoints"""
    
    base_url = "http://localhost:8000"
    
    print("🧪 Testing API Logging System")
    print("=" * 50)
    
    # Test 1: Health check
    print("\n1. Testing health check...")
    try:
        response = requests.get(f"{base_url}/health")
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.json()}")
    except Exception as e:
        print(f"   Error: {e}")
    
    # Test 2: Router status
    print("\n2. Testing router status...")
    try:
        response = requests.get(f"{base_url}/router/status")
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            print(f"   Response: {response.json()}")
    except Exception as e:
        print(f"   Error: {e}")
    
    # Test 3: Get log dates
    print("\n3. Testing get log dates...")
    try:
        response = requests.get(f"{base_url}/logs/dates")
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.json()}")
    except Exception as e:
        print(f"   Error: {e}")
    
    # Test 4: Get today's logs
    print("\n4. Testing get today's logs...")
    try:
        today = datetime.now().strftime("%Y-%m-%d")
        response = requests.get(f"{base_url}/logs?date={today}&limit=10")
        print(f"   Status: {response.status_code}")
        result = response.json()
        print(f"   Found {result.get('total', 0)} log entries for {today}")
        
        # Show first few logs
        logs = result.get('logs', [])
        if logs:
            print("   Recent logs:")
            for i, log in enumerate(logs[-3:]):  # Show last 3 logs
                print(f"     {i+1}. {log}")
    except Exception as e:
        print(f"   Error: {e}")
    
    # Test 5: Test POST request with form data
    print("\n5. Testing POST request (QA search)...")
    try:
        data = {
            "project": "test_project",
            "text": "test question"
        }
        response = requests.post(f"{base_url}/qa/search", data=data)
        print(f"   Status: {response.status_code}")
        # Don't print full response as it might be large
        print(f"   Response type: {response.headers.get('content-type', 'unknown')}")
    except Exception as e:
        print(f"   Error: {e}")
    
    # Wait a bit for logs to be written
    time.sleep(1)
    
    # Test 6: Check logs again after making requests
    print("\n6. Checking logs after making requests...")
    try:
        today = datetime.now().strftime("%Y-%m-%d")
        response = requests.get(f"{base_url}/logs?date={today}&limit=20")
        print(f"   Status: {response.status_code}")
        result = response.json()
        print(f"   Total logs: {result.get('total', 0)}")
        print(f"   Total in file: {result.get('total_in_file', 0)}")
        
        # Show recent logs
        logs = result.get('logs', [])
        if logs:
            print("   Most recent logs:")
            for i, log in enumerate(logs[-5:]):  # Show last 5 logs
                print(f"     {i+1}. {log[:100]}...")  # Truncate long logs
    except Exception as e:
        print(f"   Error: {e}")
    
    print("\n" + "=" * 50)
    print("✅ Logging test completed!")


def test_log_cleanup():
    """Test log cleanup functionality"""
    
    base_url = "http://localhost:8000"
    
    print("\n🧹 Testing Log Cleanup")
    print("=" * 30)
    
    try:
        # Test cleanup with 30 days (default)
        response = requests.post(f"{base_url}/logs/cleanup?days_to_keep=30")
        print(f"Status: {response.status_code}")
        print(f"Response: {response.json()}")
    except Exception as e:
        print(f"Error: {e}")


if __name__ == "__main__":
    print("🚀 Starting API Logging Tests")
    print("Make sure the API server is running on http://localhost:8000")
    print()
    
    # Wait for user confirmation
    input("Press Enter to continue...")
    
    # Run tests
    test_api_logging()
    test_log_cleanup()
    
    print("\n🎉 All tests completed!")
