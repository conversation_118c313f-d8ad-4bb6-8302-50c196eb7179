#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script cho endpoint /qa/search
"""

import requests
import json
from libs.collection_model import Collection

def test_qa_search_endpoint():
    """Test endpoint /qa/search"""
    
    print("="*80)
    print(" TEST ENDPOINT /qa/search ")
    print("="*80)
    
    # URL của API
    base_url = "http://localhost:8000"
    endpoint = f"{base_url}/qa/search"
    
    # Kiểm tra collections có sẵn
    print("\n🔍 Kiểm tra collections có sẵn:")
    try:
        collection_model = Collection()
        collections = collection_model.get_all()
        
        if not collections:
            print("❌ Không có collections nào trong database")
            print("💡 Hãy tạo collections trước khi test")
            return
        
        print("✅ Collections có sẵn:")
        for collection in collections:
            print(f"  - Name: {collection['name']}")
            print(f"    Display Name: {collection['display_name']}")
            print(f"    Description: {collection.get('description', 'N/A')}")
            print()
        
        # Lấy collection đầu tiên để test
        test_collection = collections[0]
        project_name = test_collection['display_name']
        
    except Exception as e:
        print(f"❌ Lỗi khi lấy collections: {e}")
        return
    
    # Test cases
    test_cases = [
        {
            "name": "Test hợp lệ",
            "data": {
                "project": project_name,
                "text": "Cân đối thực đơn tự động thế nào?"
            },
            "expected_status": 200
        },
        {
            "name": "Test project không tồn tại",
            "data": {
                "project": "NonExistentProject",
                "text": "Test question"
            },
            "expected_status": 404
        },
        {
            "name": "Test project trống",
            "data": {
                "project": "",
                "text": "Test question"
            },
            "expected_status": 400
        },
        {
            "name": "Test text trống",
            "data": {
                "project": project_name,
                "text": ""
            },
            "expected_status": 400
        }
    ]
    
    # Chạy test cases
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{'='*60}")
        print(f"🧪 Test {i}: {test_case['name']}")
        print(f"{'='*60}")
        
        try:
            # Gửi request
            response = requests.post(endpoint, data=test_case['data'])
            
            print(f"📤 Request data: {test_case['data']}")
            print(f"📥 Status code: {response.status_code}")
            print(f"🎯 Expected status: {test_case['expected_status']}")
            
            # Kiểm tra status code
            if response.status_code == test_case['expected_status']:
                print("✅ Status code đúng")
            else:
                print("❌ Status code không đúng")
            
            # Parse response
            try:
                response_data = response.json()
                print(f"📋 Response:")
                print(json.dumps(response_data, indent=2, ensure_ascii=False))
                
                # Kiểm tra response structure
                if response.status_code == 200:
                    required_fields = ['success', 'project', 'question', 'answer']
                    missing_fields = [field for field in required_fields if field not in response_data]
                    
                    if not missing_fields:
                        print("✅ Response structure hợp lệ")
                    else:
                        print(f"❌ Response thiếu fields: {missing_fields}")
                
            except json.JSONDecodeError:
                print("❌ Response không phải JSON hợp lệ")
                print(f"Raw response: {response.text}")
                
        except requests.exceptions.ConnectionError:
            print("❌ Không thể kết nối đến API server")
            print("💡 Hãy đảm bảo server đang chạy tại http://localhost:8000")
            break
        except Exception as e:
            print(f"❌ Lỗi khi gửi request: {e}")
    
    print(f"\n{'='*80}")
    print("🏁 Hoàn thành test endpoint /qa/search")
    print(f"{'='*80}")

def test_api_health():
    """Test health endpoint trước"""
    print("🔍 Kiểm tra API health...")
    try:
        response = requests.get("http://localhost:8000/health")
        if response.status_code == 200:
            print("✅ API server đang hoạt động")
            return True
        else:
            print(f"❌ API server trả về status {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Không thể kết nối đến API server")
        print("💡 Hãy chạy: python main.py")
        return False

if __name__ == "__main__":
    print("🚀 Bắt đầu test endpoint /qa/search")
    
    # Kiểm tra API health trước
    if test_api_health():
        test_qa_search_endpoint()
    else:
        print("❌ Không thể test do API server không hoạt động")
