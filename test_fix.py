"""
Test final để verify fix cho llm_usage provider và model
"""

from llm_router import LLMRout<PERSON>, LLMConfig
import json

def test_llm_usage_fix():
    """Test để verify fix cho llm_usage"""
    
    print("🧪 TEST: LLM Usage Fix Verification")
    print("="*40)
    
    # Tạo mock data giống như real scenario
    mock_results = [
        {
            "chunk_index": 1,
            "success": False,
            "error": "JSON parse error: test error",
            "data": None,
            "input_tokens": 683,
            "output_tokens": 0,
            "llm_used": "GitHub GPT-4.1",
            "provider": "github",
            "model": "gpt-4.1"
        }
    ]
    
    # Import function cần test
    from ai import merge_chunk_results_with_router
    
    # Mock router (không cần real router cho test này)
    class MockRouter:
        def get_status(self):
            return {
                'total_llms': 4,
                'available_llms': 1,
                'total_remaining_calls': 1000
            }
    
    mock_router = MockRouter()
    
    # Test merge function
    print("📊 Testing merge_chunk_results_with_router...")
    result = merge_chunk_results_with_router(
        all_results=mock_results,
        router=mock_router,
        max_tokens=3500,
        total_input_tokens=683,
        total_output_tokens=0
    )
    
    print("\n✅ RESULT:")
    print(json.dumps(result, indent=2, ensure_ascii=False))
    
    # Kiểm tra llm_usage
    llm_usage = result.get('llm_usage', {})
    
    print(f"\n🔍 LLM_USAGE ANALYSIS:")
    for llm_name, usage in llm_usage.items():
        print(f"  - {llm_name}:")
        print(f"    * Provider: {usage.get('provider')} {'✅' if usage.get('provider') != 'Unknown' else '❌'}")
        print(f"    * Model: {usage.get('model')} {'✅' if usage.get('model') != 'Unknown' else '❌'}")
        print(f"    * Chunks: {usage.get('chunks')}")
        print(f"    * Success: {usage.get('successful_chunks')}")
        print(f"    * Failed: {usage.get('failed_chunks')}")
    
    # Verify fix
    if llm_usage:
        first_llm = list(llm_usage.values())[0]
        provider_ok = first_llm.get('provider') != 'Unknown'
        model_ok = first_llm.get('model') != 'Unknown'
        
        print(f"\n🎯 FIX VERIFICATION:")
        print(f"  - Provider không còn 'Unknown': {'✅ PASS' if provider_ok else '❌ FAIL'}")
        print(f"  - Model không còn 'Unknown': {'✅ PASS' if model_ok else '❌ FAIL'}")
        
        if provider_ok and model_ok:
            print(f"\n🎉 SUCCESS: Fix đã hoạt động! Provider và Model đã được truyền đúng.")
        else:
            print(f"\n❌ FAILED: Fix chưa hoạt động đúng.")
    else:
        print(f"\n⚠️  WARNING: Không có llm_usage data để test.")

if __name__ == "__main__":
    test_llm_usage_fix()
