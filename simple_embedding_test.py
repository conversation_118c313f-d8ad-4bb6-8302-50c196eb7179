#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Simple test script để kiểm tra embedding API trực tiếp
"""

import os
import requests
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_embedding_api_direct():
    """Test the embedding API directly without using the class"""
    
    # Get environment variables
    embeddings_model = os.getenv('EMBEDDINGS_MODEL', 'text-embedding-ada-002')
    embeddings_api_base = os.getenv('EMBEDDINGS_API_BASE')
    embeddings_api_key = os.getenv('EMBEDDINGS_API_KEY')
    
    print(f"🔧 Testing embeddings configuration:")
    print(f"   Model: {embeddings_model}")
    print(f"   API Base: {embeddings_api_base}")
    print(f"   API Key: {'***' + embeddings_api_key[-4:] if embeddings_api_key else 'Not set'}")
    
    if not embeddings_api_base or not embeddings_api_key:
        print("❌ Missing required environment variables:")
        print("   - EMBEDDINGS_API_BASE")
        print("   - EMBEDDINGS_API_KEY")
        return False
    
    try:
        # Test text
        test_text = "Hello, this is a test query for embedding."
        print(f"\n🧪 Testing with text: '{test_text}'")
        
        # Prepare the request - handle API base URL properly
        api_base = embeddings_api_base.rstrip('/')

        # Check if API base already ends with /v1, if so use /embeddings, otherwise use /v1/embeddings
        if api_base.endswith('/v1'):
            url = f"{api_base}/embeddings"
        else:
            url = f"{api_base}/v1/embeddings"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {embeddings_api_key}"
        }
        
        # Try standard OpenAI format first
        payload = {
            "model": embeddings_model,
            "input": test_text
        }
        
        print(f"🔗 Calling embedding API: {url}")
        print(f"📝 Payload: {json.dumps(payload, indent=2)}")
        
        response = requests.post(url, headers=headers, json=payload, timeout=30)
        
        print(f"📊 Response status: {response.status_code}")
        print(f"📄 Response headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ API response successful")
            print(f"📋 Response structure: {json.dumps(result, indent=2)[:500]}...")
            
            # Extract embedding from response
            if 'data' in result and len(result['data']) > 0:
                embedding = result['data'][0]['embedding']
                print(f"🎯 Embedding dimension: {len(embedding)}")
                print(f"🎯 First 5 values: {embedding[:5]}")
                return True
            else:
                print(f"⚠️  Unexpected response format, trying to find embedding...")
                print(f"📋 Full response: {json.dumps(result, indent=2)}")
                return False
        else:
            print(f"❌ API call failed with status {response.status_code}")
            print(f"📄 Error response: {response.text}")
            
            # Try alternative format
            print(f"\n🔄 Trying alternative payload format...")
            alt_payload = {
                "model": embeddings_model,
                "text": test_text
            }
            
            print(f"📝 Alternative payload: {json.dumps(alt_payload, indent=2)}")
            
            alt_response = requests.post(url, headers=headers, json=alt_payload, timeout=30)
            print(f"📊 Alternative response status: {alt_response.status_code}")
            
            if alt_response.status_code == 200:
                result = alt_response.json()
                print(f"✅ Alternative API response successful")
                print(f"📋 Response structure: {json.dumps(result, indent=2)[:500]}...")
                
                if 'embedding' in result:
                    embedding = result['embedding']
                    print(f"🎯 Embedding dimension: {len(embedding)}")
                    print(f"🎯 First 5 values: {embedding[:5]}")
                    return True
                elif 'data' in result and len(result['data']) > 0:
                    embedding = result['data'][0]['embedding']
                    print(f"🎯 Embedding dimension: {len(embedding)}")
                    print(f"🎯 First 5 values: {embedding[:5]}")
                    return True
                else:
                    print(f"📋 Full alternative response: {json.dumps(result, indent=2)}")
                    return False
            else:
                print(f"❌ Alternative API call also failed with status {alt_response.status_code}")
                print(f"📄 Alternative error response: {alt_response.text}")
                return False
                
    except requests.exceptions.RequestException as e:
        print(f"❌ Network error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting direct embedding API test...")
    success = test_embedding_api_direct()
    
    if success:
        print("\n🎉 Test passed!")
    else:
        print("\n💥 Test failed!")
