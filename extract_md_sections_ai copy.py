from langchain_community.chat_models import ChatOpenAI
from langchain.prompts import ChatPromptTemplate
from langchain.chains import LLMChain
import os
import re
import docx
import sys
os.environ["OPENAI_API_KEY"] = "********************************************************"

# Đọc nội dung file Word thay cho markdown

def read_docx(file_path):
    doc = docx.Document(file_path)
    full_text = []
    # Đọc các đoạn văn thông thường, loại bỏ dòng trống dư thừa
    for para in doc.paragraphs:
        text = para.text.strip()
        if text:
            full_text.append(text)
    # Đọc nội dung trong bảng (nếu có), mỗi dòng là 1 hàng bảng, cell cách nhau bằng tab
    for table in doc.tables:
        for row in table.rows:
            row_text = []
            for cell in row.cells:
                cell_text = cell.text.strip()
                if cell_text:
                    row_text.append(cell_text)
            if row_text:
                full_text.append('\t'.join(row_text))
    # Loại bỏ các dòng trống liên tiếp, chỉ giữ 1 dòng trống giữa các đoạn
    cleaned = []
    prev_blank = False
    for line in full_text:
        if not line.strip():
            if not prev_blank:
                cleaned.append('')
            prev_blank = True
        else:
            cleaned.append(line)
            prev_blank = False
    return '\n'.join(cleaned)

# Thay thế đường dẫn file Word tại đây
if len(sys.argv) > 1:
    word_file_path = sys.argv[1]
else:
    word_file_path = 'input.docx'  # Mặc định nếu không truyền tham số
try:
    content = read_docx(word_file_path)
except Exception as e:
    print(f"Không đọc được file Word: {word_file_path}")
    print("Lỗi chi tiết:", e)
    sys.exit(1)
print(content)
# Prompt template để phân tích cấu trúc file
prompt = ChatPromptTemplate.from_template('''Bạn là một trợ lý AI. Hãy trích xuất chính xác từng phần nội dung gốc từ văn bản sau, không được cắt bỏ, rút gọn, diễn giải lại hay thay đổi bất kỳ từ nào. Chỉ phân tách và giữ nguyên bản gốc từng phần:
- Ngày tháng
- Tên hoạt động
- Mục đích
- Chuẩn bị
- Tiến hành
- Kết thúc

Nội dung:
"""
{input}
"""

Trả về kết quả dạng JSON với các trường: ngay_thang, ten_hoat_dong, muc_dich, chuan_bi, tien_hanh, ket_thuc. Mỗi trường phải giữ nguyên nội dung gốc, không được thay đổi, không được tóm tắt, không được bỏ sót bất kỳ chi tiết nào.
''')

# Khởi tạo LLMChain với OpenAI (hoặc model tương thích OpenAI API)
llm = ChatOpenAI(
    model="gemma2-9b-it", 
    temperature=0.3,
    openai_api_base="https://api.groq.com/openai/v1"  # Custom URL for LocalAI
)
chain = LLMChain(llm=llm, prompt=prompt)

def split_text(text, max_length=2000):
    """Chia nhỏ văn bản thành các đoạn không vượt quá max_length ký tự."""
    parts = []
    start = 0
    while start < len(text):
        end = start + max_length
        # Cắt tại vị trí gần nhất là xuống dòng để tránh cắt giữa câu
        if end < len(text):
            end = text.rfind('\n', start, end)
            if end == -1 or end <= start:
                end = start + max_length
        parts.append(text[start:end])
        start = end
    return parts

def split_text_with_overlap(text, max_length=2000, overlap=200):
    """Chia nhỏ văn bản thành các đoạn có phần giao nhau (overlap)."""
    parts = []
    start = 0
    text_length = len(text)
    while start < text_length:
        end = min(start + max_length, text_length)
        parts.append(text[start:end])
        if end == text_length:
            break
        start = end - overlap  # Lùi lại overlap ký tự
        if start < 0:
            start = 0
    return parts

def extract_section_by_title(text, start_title, end_title=None):
    """Trích xuất nội dung giữa hai tiêu đề markdown."""
    pattern = rf'{re.escape(start_title)}([\s\S]*?)'
    if end_title:
        pattern += rf'{re.escape(end_title)}'
    match = re.search(pattern, text)
    if match:
        return match.group(1).strip()
    return ''

if __name__ == '__main__':
    try:
        # Chia file thành các chunk có overlap để phân tích AI
        text_parts = split_text_with_overlap(content, max_length=5000, overlap=500)
        all_results = []
        for idx, part in enumerate(text_parts):
            print(f'--- Đang phân tích chunk {idx+1}/{len(text_parts)} ---')
            result = chain.invoke({"input": part})
            all_results.append(result['text'])
        print("\n\nKết quả từng chunk:")
        for res in all_results:
            print(res)
    except Exception as e:
        print("Lỗi khi gọi chain.invoke:", e)
