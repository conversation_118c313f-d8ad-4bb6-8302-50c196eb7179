#!/usr/bin/env python3
"""
Script test để kiểm tra việc xử lý JSON với nhiều ngày
"""

import json
from ai import clean_json_response

def test_single_json_object():
    """Test với một JSON object duy nhất"""
    print("=== Test 1: Single JSON Object ===")
    
    single_json = '''
    {
        "ngay_thang": "01-01-2024",
        "ten_hoat_dong": "Hoạt động 1",
        "muc_dich": "Mục đích 1",
        "chuan_bi": "Chuẩn bị 1",
        "tien_hanh": "Tiến hành 1"
    }
    '''
    
    cleaned = clean_json_response(single_json)
    print(f"Cleaned JSON: {cleaned}")
    
    # Test xem có parse được không
    try:
        parsed = json.loads(cleaned)
        print(f"Parse thành công: {type(parsed)}")
        print(f"Nội dung: {parsed}")
    except json.JSONDecodeError as e:
        print(f"Lỗi parse JSON: {e}")
    
    print()

def test_multiple_json_objects():
    """Test với nhiều JSON objects"""
    print("=== Test 2: Multiple JSON Objects ===")
    
    multiple_json = '''
    {
        "ngay_thang": "01-01-2024",
        "ten_hoat_dong": "Hoạt động 1",
        "muc_dich": "Mục đích 1",
        "chuan_bi": "Chuẩn bị 1",
        "tien_hanh": "Tiến hành 1"
    }
    
    {
        "ngay_thang": "02-01-2024",
        "ten_hoat_dong": "Hoạt động 2",
        "muc_dich": "Mục đích 2",
        "chuan_bi": "Chuẩn bị 2",
        "tien_hanh": "Tiến hành 2"
    }
    '''
    
    cleaned = clean_json_response(multiple_json)
    print(f"Cleaned JSON: {cleaned}")
    
    # Test xem có parse được không
    try:
        parsed = json.loads(cleaned)
        print(f"Parse thành công: {type(parsed)}")
        print(f"Số items: {len(parsed) if isinstance(parsed, list) else 1}")
        print(f"Nội dung: {parsed}")
    except json.JSONDecodeError as e:
        print(f"Lỗi parse JSON: {e}")
    
    print()

def test_array_handling():
    """Test với array logic mới"""
    print("=== Test 3: Array Handling Logic ===")
    
    # Test case 1: Single object - cần thêm []
    single_obj = '{"ngay_thang": "01-01-2024", "ten_hoat_dong": "Test"}'
    if not single_obj.strip().startswith('['):
        wrapped = "[" + single_obj + "]"
    else:
        wrapped = single_obj
    print(f"Single object wrapped: {wrapped}")
    
    # Test case 2: Already array - không cần thêm []
    already_array = '[{"ngay_thang": "01-01-2024", "ten_hoat_dong": "Test1"}, {"ngay_thang": "02-01-2024", "ten_hoat_dong": "Test2"}]'
    if not already_array.strip().startswith('['):
        wrapped2 = "[" + already_array + "]"
    else:
        wrapped2 = already_array
    print(f"Already array: {wrapped2}")
    
    print()

def test_markdown_formatting():
    """Test với markdown formatting"""
    print("=== Test 4: Markdown Formatting ===")
    
    markdown_json = '''```json
    {
        "ngay_thang": "01-01-2024",
        "ten_hoat_dong": "Hoạt động với markdown",
        "muc_dich": "Test markdown",
        "chuan_bi": "Chuẩn bị",
        "tien_hanh": "Tiến hành"
    }
    ```'''
    
    cleaned = clean_json_response(markdown_json)
    print(f"Cleaned JSON: {cleaned}")
    
    # Test xem có parse được không
    try:
        parsed = json.loads(cleaned)
        print(f"Parse thành công: {type(parsed)}")
        print(f"Nội dung: {parsed}")
    except json.JSONDecodeError as e:
        print(f"Lỗi parse JSON: {e}")
    
    print()

if __name__ == "__main__":
    print("🧪 Testing JSON handling fixes...\n")
    
    test_single_json_object()
    test_multiple_json_objects()
    test_array_handling()
    test_markdown_formatting()
    
    print("✅ All tests completed!")
