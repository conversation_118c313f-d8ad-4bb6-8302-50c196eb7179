import re
from typing import Dict

# Đọc nội dung file 1.md
with open('1.md', 'r', encoding='utf-8') as f:
    content = f.read()

def extract_sections(md_text: str) -> Dict[str, str]:
    result = {}
    # Ng<PERSON>y tháng
    date_match = re.search(r'^(.*?\d{4})', md_text, re.MULTILINE)
    result['ngay_thang'] = date_match.group(1).strip() if date_match else ''
    # Tên hoạt động
    activity_match = re.search(r'\*\*(.*?)\*\*', md_text)
    result['ten_hoat_dong'] = activity_match.group(1).strip() if activity_match else ''
    # Mụ<PERSON> đích
    mucdich_match = re.search(r'\*\*I\. MỤC ĐÍCH\*\*([\s\S]*?)\*\*II\.CHUẨN BỊ\*\*', md_text)
    result['muc_dich'] = mucdich_match.group(1).strip().replace('\n', ' ') if mucdich_match else ''
    # <PERSON><PERSON><PERSON> bị
    chuanbi_match = re.search(r'\*\*II\.CHUẨN BỊ\*\*([\s\S]*?)\*\*III\. TIẾN HÀNH\*\*', md_text)
    result['chuan_bi'] = chuanbi_match.group(1).strip().replace('\n', ' ') if chuanbi_match else ''
    # Tiến hành
    tienhanh_match = re.search(r'\*\*III\. TIẾN HÀNH\*\*([\s\S]*?)(\*\*\\\* Kết thúc:|\*\*\\\* Kết thúc\*\*)', md_text)
    result['tien_hanh'] = tienhanh_match.group(1).strip() if tienhanh_match else ''
    # Kết thúc
    ketthuc_match = re.search(r'\*\*\\\* Kết thúc:?\*\*([\s\S]*?)(\*\*|$)', md_text)
    result['ket_thuc'] = ketthuc_match.group(1).strip() if ketthuc_match else ''
    return result

if __name__ == '__main__':
    sections = extract_sections(content)
    for k, v in sections.items():
        print(f'--- {k.upper()} ---')
        print(v.strip(), '\n')
