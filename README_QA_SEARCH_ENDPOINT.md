# Endpoint /qa/search

## Tổng quan

Endpoint `/qa/search` cho phép tìm kiếm và trả lời câu hỏi từ các collection đã được import vào hệ thống TypeSense. Endpoint này sử dụng AI để tìm kiếm documents liên quan và tạo câu trả lời dựa trên context.

## API Specification

### Endpoint
```
POST /qa/search
```

### Request Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `project` | string | Yes | Tên project (display_name của collection trong MySQL) |
| `text` | string | Yes | Câu hỏi cần tìm kiếm và trả lời |

### Request Format
```
Content-Type: application/x-www-form-urlencoded
```

### Response Format

#### Success Response (200)
```json
{
  "success": true,
  "project": "<PERSON><PERSON><PERSON><PERSON> dục Q&A",
  "collection_name": "education_qa",
  "question": "C<PERSON> đối thực đơn tự động thế nào?",
  "answer": "Câu trả lời từ AI...",
  "confidence": 0.85,
  "llm_used": "openai/gpt-4.1",
  "sources": [
    {
      "source_file": "document1.docx",
      "similarity": 0.92,
      "content": "Nội dung liên quan..."
    }
  ],
  "total_sources": 3,
  "search_method": "vector_search",
  "processing_time": 2.5
}
```

#### Error Responses

**400 - Bad Request**
```json
{
  "success": false,
  "error": "Tham số 'project' không được để trống",
  "code": 400
}
```

**404 - Not Found**
```json
{
  "success": false,
  "error": "Không tìm thấy project 'ProjectName' trong hệ thống",
  "code": 404
}
```

**500 - Internal Server Error**
```json
{
  "success": false,
  "error": "Lỗi server: ...",
  "code": 500
}
```

## Cách sử dụng

### 1. Chuẩn bị Collections

Trước khi sử dụng endpoint, bạn cần:

1. Tạo collections trong MySQL database
2. Import dữ liệu vào TypeSense

#### Tạo Collections
```python
from libs.collection_model import Collection

collection_model = Collection()
collection_model.create(
    name="education_qa",
    display_name="Giáo dục Q&A", 
    description="Collection cho câu hỏi về giáo dục"
)
```

#### Import dữ liệu
```python
from libs.typesense_vector_db import TypesenseVectorDB

db = TypesenseVectorDB(collection_name="education_qa")
db.import_excel_to_typesense(
    file_path="qa_data.xlsx",
    title="Dữ liệu Q&A",
    collection_name="education_qa"
)
```

### 2. Gọi API

#### Sử dụng curl
```bash
curl -X POST "http://localhost:8000/qa/search" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "project=Giáo dục Q&A&text=Cân đối thực đơn tự động thế nào?"
```

#### Sử dụng Python requests
```python
import requests

response = requests.post(
    "http://localhost:8000/qa/search",
    data={
        "project": "Giáo dục Q&A",
        "text": "Cân đối thực đơn tự động thế nào?"
    }
)

result = response.json()
print(f"Answer: {result['answer']}")
```

#### Sử dụng JavaScript fetch
```javascript
const response = await fetch('http://localhost:8000/qa/search', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/x-www-form-urlencoded',
  },
  body: new URLSearchParams({
    project: 'Giáo dục Q&A',
    text: 'Cân đối thực đơn tự động thế nào?'
  })
});

const result = await response.json();
console.log('Answer:', result.answer);
```

## Validation Rules

### Project Parameter
- Không được để trống
- Phải tồn tại trong bảng `collections` với `is_active = 1`
- Tìm kiếm theo field `display_name`

### Text Parameter  
- Không được để trống
- Được trim whitespace trước khi xử lý

## Testing

### Setup Test Environment
```bash
# 1. Tạo test collections
python setup_test_collections.py

# 2. Chạy API server
python main.py

# 3. Test endpoint
python test_qa_search_endpoint.py
```

### Manual Testing
```bash
# Test hợp lệ
curl -X POST "http://localhost:8000/qa/search" \
  -d "project=Giáo dục Q&A&text=Test question"

# Test project không tồn tại
curl -X POST "http://localhost:8000/qa/search" \
  -d "project=NonExistent&text=Test question"

# Test tham số trống
curl -X POST "http://localhost:8000/qa/search" \
  -d "project=&text=Test question"
```

## Troubleshooting

### Lỗi thường gặp

1. **"Không tìm thấy project"**
   - Kiểm tra collection có tồn tại trong MySQL không
   - Kiểm tra `display_name` có chính xác không
   - Kiểm tra `is_active = 1`

2. **"Lỗi kết nối TypeSense"**
   - Kiểm tra TypeSense server có chạy không
   - Kiểm tra cấu hình trong `.env`

3. **"Không có dữ liệu để trả lời"**
   - Kiểm tra collection có dữ liệu không
   - Thử giảm threshold trong search

### Debug Mode
Để debug, bạn có thể xem logs trong console khi chạy API server:
```bash
python main.py
```

## Performance Notes

- Endpoint sử dụng vector search với embedding
- Thời gian xử lý phụ thuộc vào:
  - Kích thước collection
  - Độ phức tạp của câu hỏi
  - LLM được sử dụng
- Recommend cache kết quả cho các câu hỏi phổ biến
