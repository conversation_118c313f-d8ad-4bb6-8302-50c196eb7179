#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Demo script cho việc import và sử dụng dữ liệu Excel Dinh dưỡng với Typesense
"""

import os
import sys
from libs.typesense_vector_db import TypesenseVectorDB

def main():
    print("🍎 Demo Hệ thống Q&A Dinh dưỡng")
    print("=" * 50)
    
    # Khởi tạo database
    collection_name = "dinh_duong_qa"
    db = TypesenseVectorDB(collection_name=collection_name)
    
    # Kiểm tra file Excel
    excel_file = "Dinh dưỡng.xlsx"
    if not os.path.exists(excel_file):
        print(f"❌ Không tìm thấy file {excel_file}")
        print("Vui lòng đặt file Excel vào thư mục hiện tại")
        return
    
    print(f"📁 Tìm thấy file: {excel_file}")
    
    # Menu lựa chọn
    while True:
        print("\n🔧 Chọn chức năng:")
        print("1. Import dữ liệu từ Excel")
        print("2. Tìm kiếm documents")
        print("3. Hỏi đáp với AI")
        print("4. Xem thống kê collection")
        print("5. Thoát")
        
        choice = input("\nNhập lựa chọn (1-5): ").strip()
        
        if choice == "1":
            import_excel_data(db, excel_file)
        elif choice == "2":
            search_documents(db)
        elif choice == "3":
            qa_with_ai(db)
        elif choice == "4":
            show_collection_stats(db)
        elif choice == "5":
            print("👋 Tạm biệt!")
            break
        else:
            print("❌ Lựa chọn không hợp lệ!")

def import_excel_data(db, excel_file):
    """Import dữ liệu từ file Excel"""
    print(f"\n📊 Đang import dữ liệu từ {excel_file}...")
    
    try:
        result = db.import_excel_to_typesense(
            file_path=excel_file,
            title="Dữ liệu Q&A Dinh dưỡng",
            metadata={
                "category": "nutrition",
                "source": "dinh_duong_excel",
                "imported_by": "demo_script"
            }
        )
        
        if result["success"]:
            print("✅ Import thành công!")
            print(f"   📄 Tổng số dòng: {result['total_rows']}")
            print(f"   📝 Documents đã import: {result['imported_documents']}")
            print(f"   ⏭️  Documents bị bỏ qua (trùng lặp): {result['skipped_duplicates']}")
        else:
            print(f"❌ Import thất bại: {result['error']}")
            
    except Exception as e:
        print(f"❌ Lỗi: {e}")

def search_documents(db):
    """Tìm kiếm documents"""
    print("\n🔍 Tìm kiếm documents")
    
    while True:
        query = input("\nNhập từ khóa tìm kiếm (hoặc 'back' để quay lại): ").strip()
        if query.lower() == 'back':
            break
            
        if not query:
            print("❌ Vui lòng nhập từ khóa!")
            continue
            
        try:
            result = db.search_similar_documents(query, limit=5, threshold=0.3)
            
            if result["success"] and result["total_found"] > 0:
                print(f"✅ Tìm thấy {result['total_found']} kết quả:")
                
                for i, doc in enumerate(result["documents"], 1):
                    metadata = doc.get('metadata', {})
                    chuc_nang = metadata.get('chuc_nang', 'N/A')
                    cau_hoi = metadata.get('cau_hoi', 'N/A')
                    dap_an = metadata.get('dap_an', 'N/A')
                    
                    print(f"\n📄 Kết quả {i}:")
                    print(f"   🏷️  Chức năng: {chuc_nang}")
                    print(f"   ❓ Câu hỏi: {cau_hoi[:80]}...")
                    print(f"   💬 Đáp án: {dap_an[:100]}...")
                    print(f"   📊 Similarity: {doc['similarity']:.3f}")
            else:
                print("❌ Không tìm thấy kết quả phù hợp")
                
        except Exception as e:
            print(f"❌ Lỗi tìm kiếm: {e}")

def qa_with_ai(db):
    """Hỏi đáp với AI"""
    print("\n🤖 Hỏi đáp với AI")
    print("Bạn có thể hỏi về các chủ đề liên quan đến dinh dưỡng")
    
    # Gợi ý một số câu hỏi
    sample_questions = [
        "Làm thế nào để cấu hình nhóm trẻ?",
        "Cách thiết lập thực đơn bữa ăn?",
        "Hướng dẫn cài đặt định mức dinh dưỡng?",
        "Cách tạo báo cáo thống kê dinh dưỡng?",
        "Quy trình cân đối khẩu phần hàng ngày?"
    ]
    
    print("\n💡 Gợi ý một số câu hỏi:")
    for i, q in enumerate(sample_questions, 1):
        print(f"   {i}. {q}")
    
    while True:
        question = input("\nNhập câu hỏi (hoặc 'back' để quay lại): ").strip()
        if question.lower() == 'back':
            break
            
        if not question:
            print("❌ Vui lòng nhập câu hỏi!")
            continue
            
        try:
            print(f"\n🤔 Đang suy nghĩ về câu hỏi: {question}")
            
            result = db.search_and_answer(
                question=question,
                limit=3,
                threshold=0.3
            )
            
            if result["success"]:
                print(f"\n✅ Trả lời từ {result.get('llm_used', 'AI')}:")
                print(f"📊 Confidence: {result.get('confidence', 0):.3f}")
                print(f"📚 Tham khảo {result.get('total_documents_found', 0)} documents")
                
                print(f"\n💬 Câu trả lời:")
                print("-" * 40)
                print(result["answer"])
                print("-" * 40)
                
                if result.get("sources"):
                    print(f"\n📖 Nguồn tham khảo:")
                    for i, source in enumerate(result["sources"], 1):
                        metadata = source.get('metadata', {})
                        chuc_nang = metadata.get('chuc_nang', 'N/A')
                        cau_hoi = metadata.get('cau_hoi', 'N/A')
                        print(f"   {i}. {chuc_nang}: {cau_hoi[:60]}...")
                        print(f"      (Similarity: {source['similarity']:.3f})")
            else:
                print(f"❌ Không thể trả lời: {result['error']}")
                
        except Exception as e:
            print(f"❌ Lỗi: {e}")

def show_collection_stats(db):
    """Hiển thị thống kê collection"""
    print("\n📊 Thống kê Collection")
    
    try:
        stats = db.get_collection_stats()
        if stats:
            print(f"📁 Collection: {db.collection_name}")
            print(f"📄 Tổng số documents: {stats.get('num_documents', 0)}")
            print(f"💾 Kích thước: {stats.get('num_memory_shards', 0)} memory shards")
        else:
            print("❌ Không thể lấy thống kê")
            
    except Exception as e:
        print(f"❌ Lỗi: {e}")

if __name__ == "__main__":
    main()
