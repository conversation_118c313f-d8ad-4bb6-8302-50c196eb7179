# Word Document Processor API

API FastAPI để xử lý file Word và trích xuất thông tin sử dụng AI.

## Cài đặt

1. Cài đặt dependencies:
```bash
pip install -r requirements.txt
```

2. Tạo file `.env` với các biến môi trường cần thiết:
```env
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_TEMPERATURE=0.0
OPENAI_API_BASE=https://api.openai.com/v1
EMBEDDINGS_MODEL=text-embedding-ada-002
EMBEDDINGS_API_BASE=https://api.openai.com/v1
EMBEDDINGS_API_KEY=your_openai_api_key
```

## Chạy ứng dụng

```bash
python main.py
```

Hoặc sử dụng uvicorn trực tiếp:
```bash
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

API sẽ chạy tại: http://localhost:8000

## Endpoints

### 1. GET /
Thông tin cơ bản về API

### 2. GET /health
Kiểm tra trạng thái API

### 3. POST /process-docx
Upload và xử lý file Word

**Request:**
- `file`: File Word (.docx) cần upload
- `max_tokens`: Giới hạn số tokens cho mỗi chunk (optional, default: 7500)
- `verbose`: Hiển thị thông tin chi tiết (optional, default: false)

**Example using curl:**
```bash
curl -X POST "http://localhost:8000/process-docx" \
  -F "file=@your_document.docx" \
  -F "max_tokens=7500" \
  -F "verbose=true"
```

### 4. POST /process-docx-path
Xử lý file Word từ đường dẫn trên server

**Request:**
- `file_path`: Đường dẫn đến file Word trên server
- `max_tokens`: Giới hạn số tokens cho mỗi chunk (optional, default: 7500)
- `verbose`: Hiển thị thông tin chi tiết (optional, default: false)

**Example using curl:**
```bash
curl -X POST "http://localhost:8000/process-docx-path" \
  -F "file_path=/path/to/your/document.docx" \
  -F "max_tokens=7500" \
  -F "verbose=true"
```

### 5. POST /parse-results
Parse kết quả JSON từ AI để dễ đọc hơn

**Request Body (JSON):**
```json
[
  "JSON string result 1",
  "JSON string result 2"
]
```

## Response Format

Tất cả endpoints trả về response theo format:

```json
{
  "success": true/false,
  "message": "Thông báo",
  "results": [
    {
      "chunk_index": 1,
      "success": true,
      "data": {
        "ngay_thang": "15/06/2024",
        "ten_hoat_dong": "Hội thảo về trí tuệ nhân tạo",
        "muc_dich": "- Tìm hiểu về các ứng dụng AI trong giáo dục\n- Chia sẻ kinh nghiệm thực tiễn",
        "chuan_bi": "- Máy chiếu\n- Tài liệu\n- Phòng họp cho 50 người",
        "tien_hanh": "- 9:00 - 9:30: Đăng ký tham dự\n- 9:30 - 11:00: Phần thuyết trình chính",
        "ket_thuc": "- Tổng kết nội dung\n- Đánh giá hiệu quả\n- Kế hoạch tiếp theo"
      },
      "raw_text": "JSON string gốc từ AI"
    },
    {
      "chunk_index": 2,
      "success": false,
      "error": "JSON parse error: ...",
      "data": null,
      "raw_text": "Text gốc không parse được"
    }
  ],
  "file_info": {
    "filename": "tên file",
    "size": "kích thước file"
  }
}
```

### Kết quả mới

- `results` là array của objects, mỗi object chứa:
  - `chunk_index`: Số thứ tự chunk
  - `success`: True/False cho biết có parse được JSON không
  - `data`: Object chứa dữ liệu đã extract (nếu success = true)
  - `raw_text`: Text gốc từ AI
  - `error`: Thông báo lỗi (nếu success = false)

## Swagger UI

Truy cập http://localhost:8000/docs để xem Swagger UI với giao diện tương tác.

## ReDoc

Truy cập http://localhost:8000/redoc để xem ReDoc documentation.

## Lưu ý

- API chỉ hỗ trợ file Word định dạng .docx
- Cần cấu hình OpenAI API key trong file .env
- File upload sẽ được xử lý trong thư mục tạm và tự động xóa sau khi xử lý
- Kết quả trả về đã được format JSON tự động với thông tin chi tiết về từng chunk
- Mỗi chunk sẽ có trường `success` để biết có parse được JSON hay không
- Sử dụng endpoint `/parse-results` để có summary và format đẹp hơn
