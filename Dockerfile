FROM ubuntu:20.04

# Set working directory
WORKDIR /app
RUN apt-get update && apt install software-properties-common -y
RUN add-apt-repository ppa:deadsnakes/ppa
# Install LAPACK and other dependencies
RUN apt-get update && apt-get install -y \
    nano htop wget curl git \
    build-essential zlib1g-dev \
    libncurses5-dev libgdbm-dev libnss3-dev libssl-dev \
    libreadline-dev libffi-dev libsqlite3-dev libcairo2-dev pkg-config \
    python3.12 python3.12-venv python3.12-dev\
    && rm -rf /var/lib/apt/lists/*

RUN curl -sS https://bootstrap.pypa.io/get-pip.py | python3.12

# Copy the app files
COPY . .

# Install dependencies
RUN pip3 install --no-cache-dir -r requirements_ubuntu20.04.txt

# Expose the application port
EXPOSE 8000

CMD ["python3", "main.py"]
# Run the application using Gunicorn for production
#CMD ["gunicorn", "-w", "4", "-b", "0.0.0.0:5556", "app:app"]
