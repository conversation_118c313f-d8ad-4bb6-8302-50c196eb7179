[{"name": "GitHub GPT-4.1 #1", "provider": "github", "model": "gpt-4.1", "api_base_url": "https://models.inference.ai.azure.com", "api_token": "*********************************************************************************************", "max_calls": 50, "remaining_calls": 49, "max_input_tokens": 1000000, "temperature": 0.3, "enabled": 1}, {"name": "GitHub GPT-4.1 #2", "provider": "github", "model": "gpt-4.1", "api_base_url": "https://models.inference.ai.azure.com", "api_token": "*********************************************************************************************", "max_calls": 50, "remaining_calls": 49, "max_input_tokens": 1000000, "temperature": 0.3, "enabled": 1}, {"name": "OpenRouter - openrouter/cypher-alpha:free", "provider": "localai", "model": "openrouter/cypher-alpha:free", "api_base_url": "https://openrouter.ai/api/v1", "api_token": "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", "max_calls": 100, "remaining_calls": 93, "max_input_tokens": 10000, "temperature": 0.3, "enabled": 1}]