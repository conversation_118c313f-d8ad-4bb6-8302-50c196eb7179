#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test standalone logging functionality
"""

import os
import sys
import json
from datetime import datetime

# Add libs to path
sys.path.append('libs')

from libs.request_logger import RequestLogger


def test_request_logger():
    """Test RequestLogger class directly"""
    
    print("🧪 Testing RequestLogger Class")
    print("=" * 40)
    
    # Tạo thư mục logs
    os.makedirs("logs", exist_ok=True)
    
    # Khởi tạo logger
    logger = RequestLogger()
    
    print("✅ RequestLogger initialized")
    
    # Test 1: Log một request GET
    print("\n1. Testing GET request logging...")
    logger.log_request(
        method="GET",
        url="http://localhost:8000/health",
        headers={"user-agent": "test-client", "accept": "application/json"},
        query_params={},
        client_ip="127.0.0.1",
        user_agent="test-client/1.0"
    )
    
    # Test 2: Log response tương ứng
    logger.log_response(
        method="GET",
        url="http://localhost:8000/health",
        status_code=200,
        response_time=0.123,
        response_size=45
    )
    print("✅ GET request/response logged")
    
    # Test 3: Log một request POST với body
    print("\n2. Testing POST request with body...")
    logger.log_request(
        method="POST",
        url="http://localhost:8000/qa/search",
        headers={"content-type": "application/x-www-form-urlencoded", "user-agent": "test-client"},
        query_params={},
        body="project=test_project&text=what is AI?",
        client_ip="*************",
        user_agent="test-client/1.0"
    )
    
    logger.log_response(
        method="POST",
        url="http://localhost:8000/qa/search",
        status_code=404,
        response_time=1.456,
        response_size=234,
        error="Collection not found"
    )
    print("✅ POST request/response logged")
    
    # Test 4: Log request với JSON body
    print("\n3. Testing request with JSON body...")
    json_body = json.dumps({
        "query": "test query",
        "filters": {"category": "AI", "language": "en"},
        "limit": 10
    })
    
    logger.log_request(
        method="POST",
        url="http://localhost:8000/api/search",
        headers={"content-type": "application/json", "authorization": "Bearer secret-token"},
        query_params={"debug": "true"},
        body=json_body,
        client_ip="********",
        user_agent="curl/7.68.0"
    )
    
    logger.log_response(
        method="POST",
        url="http://localhost:8000/api/search",
        status_code=200,
        response_time=2.789,
        response_size=1024
    )
    print("✅ JSON request/response logged")
    
    # Test 5: Log request với body quá lớn
    print("\n4. Testing request with large body...")
    large_body = "x" * 15000  # 15KB body
    
    logger.log_request(
        method="PUT",
        url="http://localhost:8000/api/upload",
        headers={"content-type": "text/plain"},
        query_params={},
        body=large_body,
        client_ip="**********",
        user_agent="python-requests/2.28.0"
    )
    
    logger.log_response(
        method="PUT",
        url="http://localhost:8000/api/upload",
        status_code=413,
        response_time=0.050,
        response_size=67,
        error="Request entity too large"
    )
    print("✅ Large body request/response logged")
    
    # Test 6: Kiểm tra file log được tạo
    print("\n5. Checking log files...")
    today = datetime.now().strftime("%Y-%m-%d")
    log_file = f"logs/api_requests_{today}.log"
    
    if os.path.exists(log_file):
        with open(log_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print(f"✅ Log file created: {log_file}")
        print(f"   Total lines: {len(lines)}")
        
        # Hiển thị một vài dòng log
        print("\n   Sample log entries:")
        for i, line in enumerate(lines[:6], 1):  # Show first 6 lines
            print(f"   {i}. {line.strip()}")
            
        if len(lines) > 6:
            print(f"   ... and {len(lines) - 6} more lines")
    else:
        print(f"❌ Log file not found: {log_file}")
    
    # Test 7: Test cleanup function
    print("\n6. Testing cleanup function...")
    try:
        logger.cleanup_old_logs(days_to_keep=30)
        print("✅ Cleanup function executed successfully")
    except Exception as e:
        print(f"❌ Cleanup error: {e}")
    
    print("\n" + "=" * 40)
    print("🎉 All tests completed!")
    
    return log_file


def show_log_content(log_file):
    """Hiển thị nội dung file log"""
    
    print(f"\n📄 Log File Content: {log_file}")
    print("=" * 60)
    
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(content)
        
    except Exception as e:
        print(f"Error reading log file: {e}")


def main():
    """Main function"""
    print("🎯 Standalone Logger Test")
    print("=" * 50)
    print(f"📅 Current time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Test logger
    log_file = test_request_logger()
    
    # Show log content
    if log_file and os.path.exists(log_file):
        show_log_content(log_file)
    
    print("\n📋 Summary:")
    print("- RequestLogger class works correctly")
    print("- Logs are written to daily files in logs/ directory")
    print("- Both request and response information is captured")
    print("- Sensitive headers (authorization, cookie) are filtered out")
    print("- Large request bodies are truncated")
    print("- JSON bodies are parsed and logged properly")
    print("- Cleanup function is available for old log management")


if __name__ == "__main__":
    main()
