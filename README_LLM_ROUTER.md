# LLM Router - Quản lý nhiều LLM với Load Balancing

## Tổng quan

LLM Router là một hệ thống quản lý nhiều Large Language Models (LLM) với khả năng:
- **Load balancing**: Chọn ngẫu nhiên LLM với trọng số dựa trên số lư<PERSON> call còn lại
- **Rate limiting**: <PERSON> dõi và giới hạn số lư<PERSON> call cho mỗi LLM
- **Multi-provider**: Hỗ trợ OpenAI, Gemini, Groq, GitHub Models, LocalAI
- **Failover**: Tự động chuyển sang LLM khác khi một LLM hết lượt
- **Thread-safe**: An toàn khi sử dụng đa luồng

## Cài đặt

```bash
pip install langchain langchain-community langchain-openai langchain-google-genai python-dotenv
```

## Cấu hình

### 1. Tạo file cấu hình

File `llm_config.json` sẽ được tạo tự động với template mặc định:

```json
[
  {
    "name": "GPT-4o-mini",
    "provider": "openai",
    "model": "gpt-4o-mini", 
    "api_base_url": "https://api.openai.com/v1",
    "api_token": "your-openai-api-key",
    "max_calls": 100,
    "remaining_calls": 100,
    "temperature": 0.0,
    "enabled": true
  },
  {
    "name": "Groq Llama",
    "provider": "groq",
    "model": "llama-3.3-70b-versatile",
    "api_base_url": "https://api.groq.com/openai/v1", 
    "api_token": "your-groq-api-key",
    "max_calls": 200,
    "remaining_calls": 200,
    "temperature": 0.0,
    "enabled": true
  }
]
```

### 2. Cập nhật API Keys

Thay thế `your-xxx-api-key` bằng API keys thật của bạn trong file config.

Hoặc tạo file `.env`:

```env
OPENAI_API_KEY=sk-xxx
GROQ_API_KEY=gsk_xxx  
GEMINI_API_KEY=AIxxx
GITHUB_TOKEN=ghp_xxx
```

## Sử dụng

### 1. Sử dụng Router cơ bản

```python
from llm_router import LLMRouter

# Tạo router
router = LLMRouter()

# Hiển thị trạng thái
router.print_status()

# Gửi request
result = router.make_request("Xin chào, bạn là ai?")
if result and result['success']:
    print(f"Response: {result['response']}")
    print(f"Sử dụng: {result['llm_name']}")
```

### 2. Xử lý file Word với Router

```python
from ai import main_with_router

# Xử lý file Word
main_with_router('document.docx')
```

### 3. Command Line Interface

```bash
# Hiển thị trạng thái router
python ai.py --router-status

# Reset tất cả lượt call
python ai.py --router-reset

# Xử lý file Word với router
python ai.py --router document.docx

# Demo router
python example_router.py --demo
```

## API Reference

### LLMConfig Class

```python
@dataclass
class LLMConfig:
    name: str              # Tên LLM
    provider: str          # Provider (openai, gemini, groq, github, localai)
    model: str             # Tên model
    api_base_url: str      # Base URL của API
    api_token: str         # API token/key
    max_calls: int         # Số lượt call tối đa
    remaining_calls: int   # Số lượt call còn lại
    temperature: float     # Temperature (default: 0.0)
    enabled: bool          # Có enable hay không (default: True)
```

### LLMRouter Class

#### Khởi tạo
```python
router = LLMRouter(config_file="llm_config.json")
```

#### Methods chính

```python
# Gửi request
result = router.make_request(prompt, **kwargs)

# Chọn LLM ngẫu nhiên
llm_config = router.select_random_llm()

# Lấy danh sách LLM khả dụng
available = router.get_available_llms()

# Thêm/xóa LLM
router.add_llm(llm_config)
router.remove_llm("LLM Name")

# Enable/Disable LLM
router.enable_llm("LLM Name")
router.disable_llm("LLM Name")

# Reset lượt call
router.reset_all_calls()

# Lấy trạng thái
status = router.get_status()
router.print_status()
```

## Hỗ trợ Providers

### OpenAI
```python
LLMConfig(
    name="GPT-4o",
    provider="openai",
    model="gpt-4o",
    api_base_url="https://api.openai.com/v1",
    api_token="sk-xxx"
)
```

### Groq
```python
LLMConfig(
    name="Groq Llama",
    provider="groq", 
    model="llama-3.3-70b-versatile",
    api_base_url="https://api.groq.com/openai/v1",
    api_token="gsk_xxx"
)
```

### GitHub Models
```python
LLMConfig(
    name="GitHub GPT-4o",
    provider="github",
    model="gpt-4o",
    api_base_url="https://models.inference.ai.azure.com",
    api_token="ghp_xxx"
)
```

### Google Gemini
```python
LLMConfig(
    name="Gemini Pro",
    provider="gemini",
    model="gemini-1.5-pro", 
    api_base_url="",  # Không cần base URL
    api_token="AIxxx"
)
```

### LocalAI
```python
LLMConfig(
    name="Local Model",
    provider="localai",
    model="custom-model",
    api_base_url="http://localhost:8080/v1",
    api_token="not-needed"
)
```

## Tính năng nâng cao

### 1. Weighted Random Selection

Router sử dụng số lượt call còn lại làm trọng số để chọn LLM:
- LLM có nhiều lượt call hơn có xác suất được chọn cao hơn
- Đảm bảo phân bố đều các request

### 2. Thread Safety

Router sử dụng `threading.Lock()` để đảm bảo an toàn khi:
- Cập nhật số lượt call
- Lưu config
- Chọn LLM

### 3. Automatic Failover

Nếu không có LLM nào khả dụng, hệ thống sẽ:
- Fallback về method xử lý cũ
- Thông báo lỗi rõ ràng
- Không làm crash application

### 4. Usage Tracking

Router theo dõi chi tiết:
- Số lượt call đã sử dụng/còn lại
- Success rate của từng LLM
- Token usage
- Response time (có thể mở rộng)

## Examples

### Example 1: Basic Usage

```python
from llm_router import LLMRouter

router = LLMRouter()
router.print_status()

# Gửi 5 requests
for i in range(5):
    result = router.make_request(f"Câu hỏi số {i+1}: Hôm nay thế nào?")
    if result and result['success']:
        print(f"Request {i+1}: {result['llm_name']} - {result['response'][:50]}...")

router.print_status()
```

### Example 2: Custom Configuration

```python
from llm_router import LLMRouter, LLMConfig

# Tạo router với config tùy chỉnh
router = LLMRouter("my_config.json")

# Thêm LLM mới
custom_llm = LLMConfig(
    name="My Custom LLM",
    provider="openai",
    model="gpt-3.5-turbo",
    api_base_url="https://api.openai.com/v1",
    api_token="sk-xxx",
    max_calls=50,
    remaining_calls=50
)

router.add_llm(custom_llm)
```

### Example 3: Batch Processing

```python
from llm_router import LLMRouter

router = LLMRouter()
prompts = ["Câu hỏi 1", "Câu hỏi 2", "Câu hỏi 3"]

results = []
for prompt in prompts:
    result = router.make_request(prompt)
    results.append(result)

# Phân tích kết quả
successful = [r for r in results if r and r['success']]
print(f"Thành công: {len(successful)}/{len(results)}")
```

## Troubleshooting

### Lỗi thường gặp

1. **"Không có LLM nào khả dụng"**
   - Kiểm tra API keys trong config file
   - Đảm bảo còn lượt call (remaining_calls > 0)
   - Kiểm tra enabled = true

2. **"JSON parse error"**
   - LLM trả về format không đúng
   - Thử adjust prompt template
   - Kiểm tra model có hỗ trợ JSON output không

3. **"Connection error"**
   - Kiểm tra network connection
   - Verify API base URLs
   - Check API key permissions

### Debug Mode

```python
router = LLMRouter()
result = router.make_request("test", verbose=True)
```

### Reset khi gặp lỗi

```python
# Reset tất cả lượt call
router.reset_all_calls()

# Hoặc reset specific LLM
router.update_llm_calls("LLM Name", 100)
```

## Mở rộng

### Thêm Provider mới

1. Cập nhật `create_llm_instance()` method
2. Thêm provider vào enum
3. Test với provider mới

### Thêm metrics

```python
# Có thể mở rộng để track thêm:
- Response time
- Error rates  
- Cost per request
- Quality scores
```

## License

MIT License
