#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test nhanh FastAPI với JSON format mới
"""

from ai import extract_sections_from_docx
import json

def test_json_format():
    """Test format JSON mới"""
    print("=== TEST JSON FORMAT MỚI ===")
    
    # Test với file có sẵn
    test_files = ["1.docx", "2.docx", "3.docx"]
    
    for file_path in test_files:
        try:
            print(f"\nTest với file: {file_path}")
            results = extract_sections_from_docx(file_path, max_tokens=5000, verbose=True)
            
            print(f"\nĐã nhận được {len(results)} chunk(s):")
            
            for result in results:
                chunk_idx = result.get('chunk_index', 0)
                success = result.get('success', False)
                
                print(f"\nChunk {chunk_idx}:")
                print(f"  Success: {success}")
                
                if success:
                    data = result.get('data', {})
                    print(f"  Fields found: {list(data.keys())}")
                    for key, value in data.items():
                        # Chỉ hiển thị 50 ký tự đầu
                        display_value = str(value)[:50] + "..." if len(str(value)) > 50 else str(value)
                        print(f"    {key}: {display_value}")
                else:
                    print(f"  Error: {result.get('error', 'Unknown error')}")
            
            break  # Test thành công với file đầu tiên
            
        except FileNotFoundError:
            print(f"File {file_path} không tồn tại")
            continue
        except Exception as e:
            print(f"Lỗi với file {file_path}: {e}")
            continue
    else:
        print("Không tìm thấy file nào để test")

if __name__ == "__main__":
    test_json_format()
