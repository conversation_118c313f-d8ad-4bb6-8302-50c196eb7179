#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script để tạo sample collections cho việc test endpoint /qa/search
"""

from libs.collection_model import Collection

def setup_test_collections():
    """Tạo các collections mẫu cho test"""
    
    print("="*80)
    print(" SETUP TEST COLLECTIONS ")
    print("="*80)
    
    # Khởi tạo Collection model
    collection_model = Collection()
    
    # Danh sách collections mẫu
    test_collections = [
        {
            "name": "education_qa",
            "display_name": "Giáo dục Q&A",
            "description": "Collection cho câu hỏi đáp về giáo dục"
        },
        {
            "name": "nutrition_qa", 
            "display_name": "Dinh dưỡng Q&A",
            "description": "Collection cho câu hỏi đáp về dinh dưỡng"
        },
        {
            "name": "system_help",
            "display_name": "Trợ giúp hệ thống",
            "description": "Collection cho hướng dẫn sử dụng hệ thống"
        },
        {
            "name": "general_faq",
            "display_name": "FAQ Tổng quát",
            "description": "Collection cho câu hỏi thường gặp"
        }
    ]
    
    print("🔧 Tạo collections mẫu...")
    
    created_count = 0
    existing_count = 0
    error_count = 0
    
    for collection_data in test_collections:
        name = collection_data["name"]
        display_name = collection_data["display_name"]
        description = collection_data["description"]
        
        print(f"\n📝 Xử lý collection: {name}")
        
        try:
            # Kiểm tra xem collection đã tồn tại chưa
            if collection_model.exists(name):
                print(f"  ✅ Collection '{name}' đã tồn tại")
                existing_count += 1
            else:
                # Tạo collection mới
                if collection_model.create(name, display_name, description):
                    print(f"  ✅ Đã tạo collection '{name}' ({display_name})")
                    created_count += 1
                else:
                    print(f"  ❌ Lỗi tạo collection '{name}'")
                    error_count += 1
                    
        except Exception as e:
            print(f"  ❌ Lỗi xử lý collection '{name}': {e}")
            error_count += 1
    
    # Tổng kết
    print(f"\n{'='*60}")
    print("📊 KẾT QUẢ SETUP:")
    print(f"  ✅ Đã tạo mới: {created_count} collections")
    print(f"  ℹ️  Đã tồn tại: {existing_count} collections")
    print(f"  ❌ Lỗi: {error_count} collections")
    print(f"{'='*60}")
    
    # Hiển thị danh sách collections hiện tại
    print("\n🔍 DANH SÁCH COLLECTIONS HIỆN TẠI:")
    try:
        collections = collection_model.get_all()
        if collections:
            for collection in collections:
                print(f"  - Name: {collection['name']}")
                print(f"    Display Name: {collection['display_name']}")
                print(f"    Description: {collection.get('description', 'N/A')}")
                print(f"    Active: {collection.get('is_active', 'N/A')}")
                print()
        else:
            print("  ❌ Không có collections nào")
    except Exception as e:
        print(f"  ❌ Lỗi lấy danh sách collections: {e}")

def check_database_connection():
    """Kiểm tra kết nối database"""
    print("🔍 Kiểm tra kết nối database...")
    
    try:
        collection_model = Collection()
        # Thử lấy danh sách collections để test connection
        collections = collection_model.get_all()
        print("✅ Kết nối database thành công")
        return True
    except Exception as e:
        print(f"❌ Lỗi kết nối database: {e}")
        print("💡 Hãy kiểm tra:")
        print("  - File .env có cấu hình database đúng không")
        print("  - MySQL server có đang chạy không")
        print("  - Bảng collections đã được tạo chưa")
        return False

if __name__ == "__main__":
    print("🚀 Bắt đầu setup test collections")
    
    # Kiểm tra database connection trước
    if check_database_connection():
        setup_test_collections()
        print("\n🎉 Setup hoàn thành!")
        print("💡 Bây giờ bạn có thể chạy: python test_qa_search_endpoint.py")
    else:
        print("\n❌ Setup thất bại do lỗi database connection")
