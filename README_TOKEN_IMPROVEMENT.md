# 🔧 Token Counting Improvement

## Vấn đề trước đây
- Sử dụng phương pháp ước tính đơn giản: `len(text) // 3`
- **Không chính xác**, đặc biệt với:
  - Tiếng Việt: sai đến **75%**
  - Code: sai đến **50%**
  - <PERSON><PERSON><PERSON> ngôn ngữ khác tiếng Anh

## Gi<PERSON>i pháp mới
- S<PERSON> dụng **tiktoken** - thư viện chính thức của OpenAI
- **Cache encoding** để tối ưu hiệu suất
- **Fallback** về phương pháp cũ nếu tiktoken không có

## Kết quả cải thiện

### 🎯 Độ chính xác
| Loại văn bản | Phương pháp cũ | Tiktoken | C<PERSON>i thiện |
|--------------|----------------|----------|-----------|
| Tiếng Anh    | 11 tokens      | 9 tokens | 18% ch<PERSON>h xác hơn |
| Tiếng Việt   | 16 tokens      | 28 tokens| 75% chính xác hơn |
| Code         | 64 tokens      | 31 tokens| 51% chính xác hơn |

### ⚡ Hiệu suất (với cache)
- **Lần đầu**: ~165ms (khởi tạo)
- **Các lần sau**: ~0.02ms (cache)
- **Tổng**: Chỉ chậm hơn ~298x thay vì 172,000x

## Cách sử dụng

### Basic usage
```python
from ai import count_tokens

# Chính xác với model cụ thể
tokens = count_tokens("Xin chào thế giới", "gpt-4")
print(f"Tokens: {tokens}")  # Output: 9 tokens
```

### Cache management
```python
from ai import get_cache_info, clear_token_cache

# Xem thông tin cache
print(get_cache_info())
# {'available': True, 'cached_models': ['gpt-4'], 'cache_size': 1}

# Xóa cache khi cần thiết
clear_token_cache()
```

### Models được hỗ trợ
- **OpenAI**: gpt-4, gpt-3.5-turbo, gpt-4o, etc.
- **GitHub Models**: openai/gpt-4.1, openai/gpt-4.1-mini
- **Groq**: llama-3.3-70b-versatile, etc.
- **Gemini**: gemini-1.5-pro, gemini-1.5-flash, etc.
- **LocalAI**: custom models

## Tích hợp trong code

Tất cả functions đã được cập nhật tự động:
- `extract_sections_from_docx()` - tính token chính xác theo model
- `extract_sections_from_docx_with_router()` - tương thích với router
- `ensure_token_limit()` - chunking chính xác hơn

## Lưu ý
- **Requires**: `pip install tiktoken>=0.9.0`
- **Fallback**: Tự động về phương pháp cũ nếu tiktoken không có
- **Cache**: Tự động cache encoding để tăng tốc
- **Memory**: Cache có thể chiếm memory, dùng `clear_token_cache()` khi cần
