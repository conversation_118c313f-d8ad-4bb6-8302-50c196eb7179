#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FastAPI application để xử lý file Word sử dụng AI
"""

from fastapi import FastAPI, File, UploadFile, HTTPException, Form
from fastapi.responses import JSONResponse
from pydantic import BaseModel
import tempfile
import os
import json
from typing import List, Optional
import shutil

from ai import extract_sections_from_docx, get_env_var, extract_sections_from_docx_with_router
        
    Returns:
        ProcessingResponse: Kết quả xử lý từ LLM Router với thông tin chi tiết về usage
    """router
from llm_router import LLMRouter, create_router_from_env

# Khởi tạo FastAPI app
app = FastAPI(
    title="Word Document Processor",
    description="API để xử lý file Word và trích xuất thông tin sử dụng AI",
    version="1.0.0"
)

# Available models configuration
AVAILABLE_MODELS = {
    'github': get_env_var('GITHUB_MODELS', 'openai/gpt-4.1,openai/gpt-4.1-mini').split(','),
    'groq': get_env_var('GROQ_MODELS', 'openai/gpt-4.1,openai/gpt-4.1-mini').split(','),
    'localai': get_env_var('LOCALAI_MODELS').split(','),
    'gemini': get_env_var('GEMINI_MODELS', 'gemini-1.5-pro,gemini-1.5-flash,gemini-1.0-pro').split(',')
}

# Pydantic models cho request/response
class ProcessingResponse(BaseModel):
    """Response model cho kết quả xử lý"""
    success: bool
    message: str
    results: Optional[List[dict]] = None  # Đổi từ List[str] thành List[dict]
    file_info: Optional[dict] = None

class ChunkResult(BaseModel):
    """Model cho kết quả từng chunk"""
    chunk_index: int
    success: bool
    data: Optional[dict] = None
    raw_text: str
    error: Optional[str] = None

class ProcessingRequest(BaseModel):
    """Request model cho xử lý file"""
    max_tokens: Optional[int] = None  # DEPRECATED - will use LLM config
    verbose: Optional[bool] = False

class ModelSelection(BaseModel):
    """Model selection options"""
    provider: str  # 'github' or 'groq'
    model: str    # specific model name

@app.get("/")
async def root():
    """Endpoint gốc"""
    return {
        "message": "Word Document Processor API with LLM Router",
        "version": "1.0.0",
        "endpoints": {
            "POST /process-docx": "Upload và xử lý file Word (model cụ thể)",
            "POST /process-docx-path": "Xử lý file Word từ đường dẫn (model cụ thể)",
            "POST /process-docx-router": "Upload và xử lý file Word qua LLM Router",
            "POST /process-docx-path-router": "Xử lý file Word từ đường dẫn qua LLM Router",
            "GET /health": "Kiểm tra trạng thái API",
            "GET /models": "Lấy danh sách các mô hình AI có sẵn",
            "GET /router/status": "Lấy trạng thái LLM Router",
            "POST /router/reset-calls": "Reset số lượt call của tất cả LLM",
            "POST /router/enable-llm": "Enable một LLM",
            "POST /router/disable-llm": "Disable một LLM",
            "GET /router/available-llms": "Lấy danh sách LLM khả dụng"
        }
    }

@app.get("/health")
async def health_check():
    """Kiểm tra trạng thái API"""
    return {"status": "healthy", "message": "API đang hoạt động bình thường"}

@app.post("/process-docx", response_model=ProcessingResponse)
async def process_docx_upload(
    file: UploadFile = File(..., description="File Word cần xử lý (.docx)"),
    max_tokens: int = Form(None, description="DEPRECATED - Giá trị sẽ được lấy từ LLM config"),
    verbose: bool = Form(False, description="Hiển thị thông tin chi tiết"),
    model: Optional[str] = Form(None, description="Model AI để sử dụng (optional)")
):
    """
    Upload và xử lý file Word
    
    Args:
        file: File Word upload (.docx)
        max_tokens: DEPRECATED - Giá trị sẽ được lấy từ LLM config
        verbose: Hiển thị thông tin chi tiết quá trình xử lý
        model: Model AI để sử dụng (optional, có thể là: llama-3.3-70b-versatile, meta-llama/llama-4-maverick-17b-128e-instruct, openai/gpt-4.1)
        
    Returns:
        ProcessingResponse: Kết quả xử lý từ AI
    """
    
    # Kiểm tra loại file
    if not file.filename.lower().endswith('.docx'):
        raise HTTPException(
            status_code=400,
            detail="Chỉ hỗ trợ file Word (.docx). Vui lòng upload file đúng định dạng."
        )
    
    # Validate model if provided
    if model:
        all_models = []
        for provider_models in AVAILABLE_MODELS.values():
            all_models.extend(provider_models)
        
        if model not in all_models:
            raise HTTPException(
                status_code=400,
                detail=f"Model '{model}' không được hỗ trợ. Các model khả dụng: {', '.join(all_models)}"
            )
    
    # Tạo file tạm để lưu upload
    try:
        with tempfile.NamedTemporaryFile(delete=False, suffix='.docx') as temp_file:
            # Copy nội dung file upload vào file tạm
            shutil.copyfileobj(file.file, temp_file)
            temp_file_path = temp_file.name
        
        # Lấy thông tin file
        file_info = {
            "filename": file.filename,
            "content_type": file.content_type,
            "size": file.size if hasattr(file, 'size') else os.path.getsize(temp_file_path)
        }
        
        # Gọi function AI để xử lý
        results = extract_sections_from_docx(
            word_file_path=temp_file_path,
            max_tokens=max_tokens,
            verbose=verbose,
            model=model
        )
        
        return ProcessingResponse(
            success=True,
            message=f"Đã xử lý thành công file '{file.filename}' với {len(results)} chunk(s)",
            results=results,
            file_info=file_info
        )
        
    except Exception as e:
        print(e)
        raise HTTPException(
            status_code=500,
            detail=f"Lỗi khi xử lý file: {str(e)}"
        )
    finally:
        # Xóa file tạm
        if 'temp_file_path' in locals() and os.path.exists(temp_file_path):
            os.unlink(temp_file_path)

@app.post("/process-docx-path", response_model=ProcessingResponse)
async def process_docx_from_path(
    file_path: str = Form(..., description="Đường dẫn đến file Word"),
    max_tokens: int = Form(None, description="DEPRECATED - Giá trị sẽ được lấy từ LLM config"),
    verbose: bool = Form(False, description="Hiển thị thông tin chi tiết"),
    model: Optional[str] = Form(None, description="Model AI để sử dụng (optional)")
):
    """
    Xử lý file Word từ đường dẫn trên server
    
    Args:
        file_path: Đường dẫn đến file Word trên server
        max_tokens: DEPRECATED - Giá trị sẽ được lấy từ LLM config
        verbose: Hiển thị thông tin chi tiết quá trình xử lý
        model: Model AI để sử dụng (optional, có thể là: llama-3.3-70b-versatile, meta-llama/llama-4-maverick-17b-128e-instruct, openai/gpt-4.1)
        
    Returns:
        ProcessingResponse: Kết quả xử lý từ AI
    """
    
    # Kiểm tra file tồn tại
    if not os.path.exists(file_path):
        raise HTTPException(
            status_code=404,
            detail=f"File không tồn tại: {file_path}"
        )
    
    # Kiểm tra loại file
    if not file_path.lower().endswith('.docx'):
        raise HTTPException(
            status_code=400,
            detail="Chỉ hỗ trợ file Word (.docx). Vui lòng cung cấp đường dẫn đến file .docx"
        )
    
    # Validate model if provided
    if model:
        all_models = []
        for provider_models in AVAILABLE_MODELS.values():
            all_models.extend(provider_models)
        
        if model not in all_models:
            raise HTTPException(
                status_code=400,
                detail=f"Model '{model}' không được hỗ trợ. Các model khả dụng: {', '.join(all_models)}"
            )
    
    try:
        # Lấy thông tin file
        file_info = {
            "filename": os.path.basename(file_path),
            "path": file_path,
            "size": os.path.getsize(file_path)
        }
        
        # Gọi function AI để xử lý
        results = extract_sections_from_docx(
            word_file_path=file_path,
            max_tokens=max_tokens,
            verbose=verbose,
            model=model
        )
        
        return ProcessingResponse(
            success=True,
            message=f"Đã xử lý thành công file '{os.path.basename(file_path)}' với {len(results)} chunk(s)",
            results=results,
            file_info=file_info
        )
        
    except Exception as e:
        print(e)
        raise HTTPException(
            status_code=500,
            detail=f"Lỗi khi xử lý file: {str(e)}"
        )

@app.post("/parse-results")
async def parse_results(results: List[dict]):
    """
    Parse và format kết quả từ AI để dễ đọc hơn
    
    Args:
        results: List các dict result từ AI (format mới)
        
    Returns:
        dict: Kết quả đã được format
    """
    
    formatted_results = []
    successful_chunks = 0
    failed_chunks = 0
    
    for result in results:
        chunk_index = result.get("chunk_index", 0)
        success = result.get("success", False)
        
        if success:
            successful_chunks += 1
            formatted_results.append({
                "chunk_index": chunk_index,
                "status": "success",
                "extracted_data": result.get("data", {}),
                "fields_found": list(result.get("data", {}).keys()) if result.get("data") else []
            })
        else:
            failed_chunks += 1
            formatted_results.append({
                "chunk_index": chunk_index,
                "status": "failed",
                "error": result.get("error", "Unknown error"),
                "raw_text_preview": result.get("raw_text", "")[:200] + "..." if len(result.get("raw_text", "")) > 200 else result.get("raw_text", "")
            })
    
    return {
        "success": True,
        "total_chunks": len(results),
        "successful_chunks": successful_chunks,
        "failed_chunks": failed_chunks,
        "summary": {
            "processing_rate": f"{successful_chunks}/{len(results)} chunks processed successfully" if results else "0/0 chunks",
            "success_rate": f"{round(successful_chunks/len(results)*100, 1)}%" if results else "0%"
        },
        "formatted_results": formatted_results
    }

@app.get("/models")
async def get_available_models():
    """Get list of available AI models"""
    return {
        "available_models": AVAILABLE_MODELS,
        "total_models": sum(len(models) for models in AVAILABLE_MODELS.values()),
        "providers": list(AVAILABLE_MODELS.keys())
    }

@app.post("/process-docx-router", response_model=ProcessingResponse)
async def process_docx_with_router(
    file: UploadFile = File(..., description="File Word cần xử lý (.docx)"),
    max_tokens: int = Form(None, description="DEPRECATED - Giá trị sẽ được lấy từ LLM config"),
    verbose: bool = Form(False, description="Hiển thị thông tin chi tiết"),
    reset_calls: bool = Form(False, description="Reset số lượt call của tất cả LLM trước khi xử lý")
):
    """
    Upload và xử lý file Word sử dụng LLM Router
    
    Args:
        file: File Word upload (.docx)
        max_tokens: DEPRECATED - Giá trị sẽ được lấy từ LLM config
        verbose: Hiển thị thông tin chi tiết quá trình xử lý
        reset_calls: Reset số lượt call của tất cả LLM trước khi xử lý
        
    Returns:
        ProcessingResponse: Kết quả xử lý từ LLM Router với thông tin chi tiết về usage
    """
    
    # Kiểm tra loại file
    if not file.filename.lower().endswith('.docx'):
        raise HTTPException(
            status_code=400,
            detail="Chỉ hỗ trợ file Word (.docx). Vui lòng upload file đúng định dạng."
        )
    
    # Tạo file tạm để lưu upload
    try:
        with tempfile.NamedTemporaryFile(delete=False, suffix='.docx') as temp_file:
            # Copy nội dung file upload vào file tạm
            shutil.copyfileobj(file.file, temp_file)
            temp_file_path = temp_file.name
        
        # Lấy thông tin file
        file_info = {
            "filename": file.filename,
            "content_type": file.content_type,
            "size": file.size if hasattr(file, 'size') else os.path.getsize(temp_file_path)
        }
        
        # Tạo router
        router = create_router_from_env()
        
        # Reset calls nếu được yêu cầu
        if reset_calls:
            router.reset_all_calls()
            if verbose:
                print("🔄 Đã reset tất cả số lượt call")
        
        # Kiểm tra có LLM nào khả dụng không
        available_llms = router.get_available_llms()
        if not available_llms:
            raise HTTPException(
                status_code=503,
                detail="Không có LLM nào khả dụng. Vui lòng kiểm tra cấu hình API keys hoặc số lượt call."
            )
        
        if verbose:
            print(f"🤖 Router có {len(available_llms)} LLM khả dụng")
        
        # Gọi function AI Router để xử lý
        results = extract_sections_from_docx_with_router(
            word_file_path=temp_file_path,
            max_tokens=max_tokens,
            verbose=verbose,
            router=router
        )
        
        # Lấy kết quả chi tiết từ router processing
        result = results[0] if results else {}
        
        # Thêm thông tin router vào file_info
        file_info["router_info"] = {
            "total_llms": result.get("router_status", {}).get("total_llms", 0),
            "available_llms": result.get("router_status", {}).get("available_llms", 0),
            "total_remaining_calls": result.get("router_status", {}).get("total_remaining_calls", 0),
            "llm_usage": result.get("llm_usage", {}),
            "total_input_tokens": result.get("total_input_tokens", 0),
            "total_output_tokens": result.get("total_output_tokens", 0),
            "total_tokens": result.get("total_tokens", 0)
        }
        
        return ProcessingResponse(
            success=result.get("success", False),
            message=f"Đã xử lý thành công file '{file.filename}' qua LLM Router với {result.get('successful_chunks', 0)}/{result.get('total_chunks', 0)} chunks thành công",
            results=results,
            file_info=file_info
        )
        
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        print(f"❌ Lỗi khi xử lý file với Router: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Lỗi khi xử lý file với Router: {str(e)}"
        )
    finally:
        # Xóa file tạm
        if 'temp_file_path' in locals() and os.path.exists(temp_file_path):
            os.unlink(temp_file_path)


@app.post("/process-docx-path-router", response_model=ProcessingResponse)
async def process_docx_path_with_router(
    file_path: str = Form(..., description="Đường dẫn đến file Word"),
    max_tokens: int = Form(3500, description="Giới hạn số tokens cho mỗi chunk"),
    verbose: bool = Form(False, description="Hiển thị thông tin chi tiết"),
    reset_calls: bool = Form(False, description="Reset số lượt call của tất cả LLM trước khi xử lý")
):
    """
    Xử lý file Word từ đường dẫn sử dụng LLM Router
    
    Args:
        file_path: Đường dẫn đến file Word trên server
        max_tokens: Giới hạn số tokens cho mỗi chunk (mặc định 3500)
        verbose: Hiển thị thông tin chi tiết quá trình xử lý
        reset_calls: Reset số lượt call của tất cả LLM trước khi xử lý
        
    Returns:
        ProcessingResponse: Kết quả xử lý từ LLM Router với thông tin chi tiết về usage
    """
    
    # Kiểm tra file tồn tại
    if not os.path.exists(file_path):
        raise HTTPException(
            status_code=404,
            detail=f"File không tồn tại: {file_path}"
        )
    
    # Kiểm tra loại file
    if not file_path.lower().endswith('.docx'):
        raise HTTPException(
            status_code=400,
            detail="Chỉ hỗ trợ file Word (.docx). Vui lòng cung cấp đường dẫn đến file .docx"
        )
    
    try:
        # Lấy thông tin file
        file_info = {
            "filename": os.path.basename(file_path),
            "path": file_path,
            "size": os.path.getsize(file_path)
        }
        
        # Tạo router
        router = create_router_from_env()
        
        # Reset calls nếu được yêu cầu
        if reset_calls:
            router.reset_all_calls()
            if verbose:
                print("🔄 Đã reset tất cả số lượt call")
        
        # Kiểm tra có LLM nào khả dụng không
        available_llms = router.get_available_llms()
        if not available_llms:
            raise HTTPException(
                status_code=503,
                detail="Không có LLM nào khả dụng. Vui lòng kiểm tra cấu hình API keys hoặc số lượt call."
            )
        
        if verbose:
            print(f"🤖 Router có {len(available_llms)} LLM khả dụng")
        
        # Gọi function AI Router để xử lý
        results = extract_sections_from_docx_with_router(
            word_file_path=file_path,
            max_tokens=max_tokens,
            verbose=verbose,
            router=router
        )
        
        # Lấy kết quả chi tiết từ router processing
        result = results[0] if results else {}
        
        # Thêm thông tin router vào file_info
        file_info["router_info"] = {
            "total_llms": result.get("router_status", {}).get("total_llms", 0),
            "available_llms": result.get("router_status", {}).get("available_llms", 0),
            "total_remaining_calls": result.get("router_status", {}).get("total_remaining_calls", 0),
            "llm_usage": result.get("llm_usage", {}),
            "total_input_tokens": result.get("total_input_tokens", 0),
            "total_output_tokens": result.get("total_output_tokens", 0),
            "total_tokens": result.get("total_tokens", 0)
        }
        
        return ProcessingResponse(
            success=result.get("success", False),
            message=f"Đã xử lý thành công file '{os.path.basename(file_path)}' qua LLM Router với {result.get('successful_chunks', 0)}/{result.get('total_chunks', 0)} chunks thành công",
            results=results,
            file_info=file_info
        )
        
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        print(f"❌ Lỗi khi xử lý file với Router: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Lỗi khi xử lý file với Router: {str(e)}"
        )

@app.get("/router/status")
async def get_router_status():
    """
    Lấy trạng thái của LLM Router
    
    Returns:
        dict: Thông tin chi tiết về trạng thái các LLM trong router
    """
    try:
        router = create_router_from_env()
        status = router.get_status()
        
        return {
            "success": True,
            "message": "Trạng thái LLM Router",
            "router_status": status,
            "summary": {
                "total_llms": status["total_llms"],
                "available_llms": status["available_llms"],
                "unavailable_llms": status["total_llms"] - status["available_llms"],
                "total_remaining_calls": status["total_remaining_calls"],
                "can_process": status["available_llms"] > 0 and status["total_remaining_calls"] > 0
            }
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Lỗi khi lấy trạng thái router: {str(e)}"
        )


@app.post("/router/reset-calls")
async def reset_router_calls():
    """
    Reset tất cả số lượt call của các LLM về giá trị max
    
    Returns:
        dict: Kết quả reset và trạng thái mới
    """
    try:
        router = create_router_from_env()
        router.reset_all_calls()
        status = router.get_status()
        
        return {
            "success": True,
            "message": "Đã reset tất cả số lượt call thành công",
            "router_status": status
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Lỗi khi reset calls: {str(e)}"
        )


@app.post("/router/enable-llm")
async def enable_llm(
    llm_name: str = Form(..., description="Tên LLM cần enable")
):
    """
    Enable một LLM trong router
    
    Args:
        llm_name: Tên LLM cần enable
        
    Returns:
        dict: Kết quả enable và trạng thái mới
    """
    try:
        router = create_router_from_env()
        router.enable_llm(llm_name)
        status = router.get_status()
        
        return {
            "success": True,
            "message": f"Đã enable LLM '{llm_name}' thành công",
            "router_status": status
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Lỗi khi enable LLM: {str(e)}"
        )


@app.post("/router/disable-llm")
async def disable_llm(
    llm_name: str = Form(..., description="Tên LLM cần disable")
):
    """
    Disable một LLM trong router
    
    Args:
        llm_name: Tên LLM cần disable
        
    Returns:
        dict: Kết quả disable và trạng thái mới
    """
    try:
        router = create_router_from_env()
        router.disable_llm(llm_name)
        status = router.get_status()
        
        return {
            "success": True,
            "message": f"Đã disable LLM '{llm_name}' thành công",
            "router_status": status
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Lỗi khi disable LLM: {str(e)}"
        )


@app.get("/router/available-llms")
async def get_available_llms():
    """
    Lấy danh sách các LLM khả dụng (enabled và còn lượt call)
    
    Returns:
        dict: Danh sách LLM khả dụng
    """
    try:
        router = create_router_from_env()
        available_llms = router.get_available_llms()
        
        return {
            "success": True,
            "message": f"Có {len(available_llms)} LLM khả dụng",
            "available_llms": [
                {
                    "name": llm.name,
                    "provider": llm.provider,
                    "model": llm.model,
                    "remaining_calls": llm.remaining_calls,
                    "max_calls": llm.max_calls,
                    "usage_percentage": ((llm.max_calls - llm.remaining_calls) / llm.max_calls * 100) if llm.max_calls > 0 else 0
                }
                for llm in available_llms
            ],
            "total_available": len(available_llms),
            "total_remaining_calls": sum(llm.remaining_calls for llm in available_llms)
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Lỗi khi lấy danh sách LLM khả dụng: {str(e)}"
        )

# Exception handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """Xử lý HTTP exceptions"""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "success": False,
            "message": exc.detail,
            "error_type": "HTTP_ERROR"
        }
    )

@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """Xử lý các exceptions khác"""
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "message": f"Lỗi server: {str(exc)}",
            "error_type": "INTERNAL_ERROR"
        }
    )

if __name__ == "__main__":
    import uvicorn
    
    print("Khởi động Word Document Processor API...")
    print("Swagger UI: http://localhost:8000/docs")
    print("ReDoc: http://localhost:8000/redoc")
    
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
