#!/usr/bin/env python3
"""
Daily Reset Script cho LLM Router
Script này sẽ được chạy hàng ngày (có thể dùng cron job) để reset remaining_calls
"""

import sys
import os
from datetime import datetime, date
import logging
from dotenv import load_dotenv

# Load environment variables từ file .env
load_dotenv()

# Thêm path để import modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from libs.database_manager import DatabaseManager, create_database_config_from_env
from libs.llm_router import LLMRouter

# Cấu hình logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('daily_reset.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def daily_reset():
    """Thực hiện reset daily calls"""
    try:
        logger.info("🌅 Bắt đầu daily reset...")
        
        # Khởi tạo database manager
        db_manager = DatabaseManager(create_database_config_from_env())
        
        if not db_manager.connection:
            logger.error("❌ Không thể kết nối database")
            return False
        
        # Reset daily calls
        today = date.today()
        success = db_manager.reset_daily_calls(today)
        
        if success:
            logger.info(f"✅ Daily reset thành công cho ngày {today}")
            
            # Cleanup old logs (giữ lại 30 ngày)
            cleanup_success = db_manager.cleanup_old_logs(30)
            if cleanup_success:
                logger.info("✅ Cleanup old logs thành công")
            else:
                logger.warning("⚠️  Cleanup old logs không thành công")
            
            # Log thống kê
            stats = db_manager.get_request_stats()
            if stats:
                general = stats.get('general', {})
                logger.info(f"📊 Thống kê 7 ngày qua:")
                logger.info(f"   - Total requests: {general.get('total_requests', 0)}")
                logger.info(f"   - Successful: {general.get('successful_requests', 0)}")
                logger.info(f"   - Failed: {general.get('failed_requests', 0)}")
                logger.info(f"   - Total tokens: {general.get('total_input_tokens', 0) + general.get('total_output_tokens', 0)}")
            
        else:
            logger.error("❌ Daily reset thất bại")
            return False
        
        db_manager.disconnect()
        logger.info("✅ Daily reset hoàn thành")
        return True
        
    except Exception as e:
        logger.error(f"❌ Lỗi trong daily reset: {e}")
        return False

def test_database_connection():
    """Test kết nối database"""
    try:
        logger.info("🔍 Test kết nối database...")
        
        db_manager = DatabaseManager(create_database_config_from_env())
        
        if db_manager.connection:
            logger.info("✅ Kết nối database thành công")
            
            # Test basic operations
            configs = db_manager.get_all_llm_configs()
            logger.info(f"📋 Tìm thấy {len(configs)} LLM configs trong database")
            
            stats = db_manager.get_request_stats()
            if stats:
                general = stats.get('general', {})
                logger.info(f"📊 Request stats: {general.get('total_requests', 0)} total requests")
            
            db_manager.disconnect()
            return True
        else:
            logger.error("❌ Không thể kết nối database")
            return False
            
    except Exception as e:
        logger.error(f"❌ Lỗi test database: {e}")
        return False

def sync_configs():
    """Đồng bộ configs từ JSON vào database"""
    try:
        logger.info("🔄 Đồng bộ configs từ JSON vào database...")
        
        # Tạo router để load configs từ JSON
        router = LLMRouter(use_database=False)  # Không dùng database để tránh conflict
        
        if not router.llm_configs:
            logger.error("❌ Không có configs để đồng bộ")
            return False
        
        # Đồng bộ vào database
        db_manager = DatabaseManager(create_database_config_from_env())
        if not db_manager.connection:
            logger.error("❌ Không thể kết nối database")
            return False
        
        config_dicts = [config.to_dict() for config in router.llm_configs]
        success = db_manager.sync_llm_configs(config_dicts)
        
        if success:
            logger.info(f"✅ Đã đồng bộ {len(config_dicts)} configs vào database")
        else:
            logger.error("❌ Đồng bộ configs thất bại")
        
        db_manager.disconnect()
        return success
        
    except Exception as e:
        logger.error(f"❌ Lỗi đồng bộ configs: {e}")
        return False

def show_stats(days: int = 7):
    """Hiển thị thống kê"""
    try:
        logger.info(f"📊 Hiển thị thống kê {days} ngày qua...")
        
        db_manager = DatabaseManager(create_database_config_from_env())
        if not db_manager.connection:
            logger.error("❌ Không thể kết nối database")
            return False
        
        from datetime import timedelta
        start_date = date.today() - timedelta(days=days)
        stats = db_manager.get_request_stats(start_date, date.today())
        
        if not stats:
            logger.info("📊 Không có dữ liệu thống kê")
            return True
        
        print("\n" + "="*60)
        print(f"📊 THỐNG KÊ {days} NGÀY QUA")
        print("="*60)
        
        general = stats.get('general', {})
        print(f"🔢 Tổng quan:")
        print(f"   - Tổng requests: {general.get('total_requests', 0):,}")
        print(f"   - Thành công: {general.get('successful_requests', 0):,}")
        print(f"   - Thất bại: {general.get('failed_requests', 0):,}")
        print(f"   - Tỷ lệ thành công: {(general.get('successful_requests', 0) / max(general.get('total_requests', 1), 1) * 100):.1f}%")
        print(f"   - Tổng input tokens: {general.get('total_input_tokens', 0):,}")
        print(f"   - Tổng output tokens: {general.get('total_output_tokens', 0):,}")
        print(f"   - Thời gian xử lý TB: {general.get('avg_processing_time_ms', 0):.0f} ms")
        
        # Thống kê theo LLM
        llm_stats = stats.get('by_llm', [])
        if llm_stats:
            print(f"\n🤖 Theo LLM:")
            for llm in llm_stats:
                success_rate = (llm['successful'] / max(llm['requests'], 1)) * 100
                print(f"   - {llm['llm_name']} ({llm['provider']}):")
                print(f"     * Requests: {llm['requests']:,} (Success: {success_rate:.1f}%)")
                print(f"     * Tokens: {llm['input_tokens']:,} input, {llm['output_tokens']:,} output")
        
        # Thống kê theo ngày
        daily_stats = stats.get('by_day', [])
        if daily_stats:
            print(f"\n📅 Theo ngày (5 ngày gần nhất):")
            for day in daily_stats[:5]:
                success_rate = (day['successful'] / max(day['requests'], 1)) * 100
                print(f"   - {day['date']}: {day['requests']:,} requests (Success: {success_rate:.1f}%)")
        
        print("="*60)
        
        db_manager.disconnect()
        return True
        
    except Exception as e:
        logger.error(f"❌ Lỗi hiển thị thống kê: {e}")
        return False

def main():
    """Main function"""
    if len(sys.argv) < 2:
        print("Sử dụng:")
        print("  python daily_reset.py reset           # Reset daily calls")
        print("  python daily_reset.py test            # Test database connection")
        print("  python daily_reset.py sync            # Sync configs từ JSON")
        print("  python daily_reset.py stats [days]    # Hiển thị thống kê")
        return
    
    command = sys.argv[1].lower()
    
    if command == 'reset':
        success = daily_reset()
        sys.exit(0 if success else 1)
    
    elif command == 'test':
        success = test_database_connection()
        sys.exit(0 if success else 1)
    
    elif command == 'sync':
        success = sync_configs()
        sys.exit(0 if success else 1)
    
    elif command == 'stats':
        days = int(sys.argv[2]) if len(sys.argv) > 2 else 7
        success = show_stats(days)
        sys.exit(0 if success else 1)
    
    else:
        print(f"❌ Command không hợp lệ: {command}")
        sys.exit(1)

if __name__ == "__main__":
    main()
