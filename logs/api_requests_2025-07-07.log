13:36:38 | INFO | REQUEST | {"timestamp": "2025-07-07T13:36:38.521627", "method": "GET", "url": "http://localhost:8000/health", "client_ip": "127.0.0.1", "user_agent": "test-client/1.0", "query_params": {}, "headers": {"user-agent": "test-client", "accept": "application/json"}}
13:36:38 | INFO | RESPONSE | {"timestamp": "2025-07-07T13:36:38.522087", "method": "GET", "url": "http://localhost:8000/health", "status_code": 200, "response_time": 0.123, "response_size": 45}
13:36:38 | INFO | REQUEST | {"timestamp": "2025-07-07T13:36:38.522202", "method": "POST", "url": "http://localhost:8000/qa/search", "client_ip": "*************", "user_agent": "test-client/1.0", "query_params": {}, "headers": {"content-type": "application/x-www-form-urlencoded", "user-agent": "test-client"}, "body": "project=test_project&text=what is AI?"}
13:36:38 | WARNING | RESPONSE | {"timestamp": "2025-07-07T13:36:38.522323", "method": "POST", "url": "http://localhost:8000/qa/search", "status_code": 404, "response_time": 1.456, "response_size": 234, "error": "Collection not found"}
13:36:38 | INFO | REQUEST | {"timestamp": "2025-07-07T13:36:38.522488", "method": "POST", "url": "http://localhost:8000/api/search", "client_ip": "********", "user_agent": "curl/7.68.0", "query_params": {"debug": "true"}, "headers": {"content-type": "application/json"}, "body": {"query": "test query", "filters": {"category": "AI", "language": "en"}, "limit": 10}}
13:36:38 | INFO | RESPONSE | {"timestamp": "2025-07-07T13:36:38.522583", "method": "POST", "url": "http://localhost:8000/api/search", "status_code": 200, "response_time": 2.789, "response_size": 1024}
13:36:38 | INFO | REQUEST | {"timestamp": "2025-07-07T13:36:38.522670", "method": "PUT", "url": "http://localhost:8000/api/upload", "client_ip": "**********", "user_agent": "python-requests/2.28.0", "query_params": {}, "headers": {"content-type": "text/plain"}, "body": "[BODY TOO LARGE: 15000 bytes]"}
13:36:38 | WARNING | RESPONSE | {"timestamp": "2025-07-07T13:36:38.522741", "method": "PUT", "url": "http://localhost:8000/api/upload", "status_code": 413, "response_time": 0.05, "response_size": 67, "error": "Request entity too large"}
13:37:10 | INFO | REQUEST | {"timestamp": "2025-07-07T13:37:10.181289", "method": "POST", "url": "http://localhost:8000/qa/search", "client_ip": "127.0.0.1", "user_agent": "PostmanRuntime/7.44.1", "query_params": {}, "headers": {"user-agent": "PostmanRuntime/7.44.1", "accept": "*/*", "cache-control": "no-cache", "postman-token": "dff15b03-6e4d-4d99-a870-2ba202021999", "host": "localhost:8000", "accept-encoding": "gzip, deflate, br", "connection": "keep-alive", "content-type": "multipart/form-data; boundary=--------------------------017712439701873148690277", "content-length": "299"}, "body": "----------------------------017712439701873148690277\r\nContent-Disposition: form-data; name=\"project\"\r\n\r\npms_khgd\r\n----------------------------017712439701873148690277\r\nContent-Disposition: form-data; name=\"text\"\r\n\r\ncấp lại mật khẩu ?\r\n----------------------------017712439701873148690277--\r\n"}
13:37:12 | INFO | RESPONSE | {"timestamp": "2025-07-07T13:37:12.960901", "method": "POST", "url": "http://localhost:8000/qa/search", "status_code": 200, "response_time": 2.78, "response_size": 0}
13:37:53 | INFO | REQUEST | {"timestamp": "2025-07-07T13:37:53.679788", "method": "POST", "url": "http://127.0.0.1:8000/khgd/extract-content-from-docx", "client_ip": "127.0.0.1", "user_agent": "PostmanRuntime/7.44.1", "query_params": {}, "headers": {"user-agent": "PostmanRuntime/7.44.1", "accept": "*/*", "cache-control": "no-cache", "postman-token": "052d4a15-f952-4200-b1d4-8d5578bf266a", "host": "127.0.0.1:8000", "accept-encoding": "gzip, deflate, br", "connection": "keep-alive", "content-type": "multipart/form-data; boundary=--------------------------322006743299116592911405", "content-length": "30854"}, "body": "[ERROR READING BODY: 'utf-8' codec can't decode byte 0xe4 in position 221: invalid continuation byte]"}
13:38:01 | INFO | RESPONSE | {"timestamp": "2025-07-07T13:38:01.630232", "method": "POST", "url": "http://127.0.0.1:8000/khgd/extract-content-from-docx", "status_code": 200, "response_time": 7.951, "response_size": 0}
13:38:33 | INFO | REQUEST | {"timestamp": "2025-07-07T13:38:33.505384", "method": "GET", "url": "http://localhost:8000/health", "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "query_params": {}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate, zstd", "accept": "*/*", "connection": "keep-alive"}}
13:38:33 | INFO | RESPONSE | {"timestamp": "2025-07-07T13:38:33.505898", "method": "GET", "url": "http://localhost:8000/health", "status_code": 200, "response_time": 0.001, "response_size": 0}
13:38:33 | INFO | REQUEST | {"timestamp": "2025-07-07T13:38:33.509388", "method": "GET", "url": "http://localhost:8000/router/status", "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "query_params": {}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate, zstd", "accept": "*/*", "connection": "keep-alive"}}
13:38:33 | INFO | RESPONSE | {"timestamp": "2025-07-07T13:38:33.591690", "method": "GET", "url": "http://localhost:8000/router/status", "status_code": 200, "response_time": 0.082, "response_size": 0}
13:38:33 | INFO | REQUEST | {"timestamp": "2025-07-07T13:38:33.594844", "method": "GET", "url": "http://localhost:8000/logs/dates", "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "query_params": {}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate, zstd", "accept": "*/*", "connection": "keep-alive"}}
13:38:33 | INFO | RESPONSE | {"timestamp": "2025-07-07T13:38:33.597032", "method": "GET", "url": "http://localhost:8000/logs/dates", "status_code": 200, "response_time": 0.002, "response_size": 0}
13:38:33 | INFO | REQUEST | {"timestamp": "2025-07-07T13:38:33.600590", "method": "GET", "url": "http://localhost:8000/logs?date=2025-07-07&limit=10", "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "query_params": {"date": "2025-07-07", "limit": "10"}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate, zstd", "accept": "*/*", "connection": "keep-alive"}}
13:38:33 | INFO | RESPONSE | {"timestamp": "2025-07-07T13:38:33.601772", "method": "GET", "url": "http://localhost:8000/logs?date=2025-07-07&limit=10", "status_code": 200, "response_time": 0.001, "response_size": 0}
13:38:33 | INFO | REQUEST | {"timestamp": "2025-07-07T13:38:33.605459", "method": "POST", "url": "http://localhost:8000/qa/search", "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "query_params": {}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate, zstd", "accept": "*/*", "connection": "keep-alive", "content-length": "39", "content-type": "application/x-www-form-urlencoded"}, "body": "project=test_project&text=test+question"}
13:38:33 | WARNING | RESPONSE | {"timestamp": "2025-07-07T13:38:33.663660", "method": "POST", "url": "http://localhost:8000/qa/search", "status_code": 404, "response_time": 0.058, "response_size": 0}
13:38:34 | INFO | REQUEST | {"timestamp": "2025-07-07T13:38:34.667802", "method": "GET", "url": "http://localhost:8000/logs?date=2025-07-07&limit=20", "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "query_params": {"date": "2025-07-07", "limit": "20"}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate, zstd", "accept": "*/*", "connection": "keep-alive"}}
13:38:34 | INFO | RESPONSE | {"timestamp": "2025-07-07T13:38:34.668538", "method": "GET", "url": "http://localhost:8000/logs?date=2025-07-07&limit=20", "status_code": 200, "response_time": 0.001, "response_size": 0}
13:38:34 | INFO | REQUEST | {"timestamp": "2025-07-07T13:38:34.671526", "method": "POST", "url": "http://localhost:8000/logs/cleanup?days_to_keep=30", "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "query_params": {"days_to_keep": "30"}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate, zstd", "accept": "*/*", "connection": "keep-alive", "content-length": "0"}}
13:38:34 | INFO | RESPONSE | {"timestamp": "2025-07-07T13:38:34.672229", "method": "POST", "url": "http://localhost:8000/logs/cleanup?days_to_keep=30", "status_code": 200, "response_time": 0.001, "response_size": 0}
