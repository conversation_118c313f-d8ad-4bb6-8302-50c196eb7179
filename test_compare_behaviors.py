#!/usr/bin/env python3
"""
So sánh behavior trước và sau khi sửa lỗi JSON
"""

import json
from ai import clean_json_response

def old_behavior(response_text):
    """Mô phỏng behavior cũ"""
    cleaned_json = "[" + clean_json_response(response_text) + "]"
    return cleaned_json

def new_behavior(response_text):
    """Behavior mới đã được sửa"""
    cleaned_json = clean_json_response(response_text)
    
    # Kiểm tra xem cleaned_json đã là array chưa
    if not cleaned_json.strip().startswith('['):
        cleaned_json = "[" + cleaned_json + "]"
    
    return cleaned_json

def compare_behaviors():
    """So sánh behavior cũ và mới"""
    
    test_cases = [
        {
            "name": "Single JSON Object",
            "input": '{"ngay_thang": "01-01-2024", "ten_hoat_dong": "Test single"}'
        },
        {
            "name": "Multiple JSON Objects", 
            "input": '''
            {
                "ngay_thang": "01-01-2024",
                "ten_hoat_dong": "Test 1"
            }
            
            {
                "ngay_thang": "02-01-2024", 
                "ten_hoat_dong": "Test 2"
            }
            '''
        },
        {
            "name": "Already Array Format",
            "input": '[{"ngay_thang": "01-01-2024", "ten_hoat_dong": "Already array"}]'
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"=== Test Case {i}: {test_case['name']} ===")
        print(f"Input: {test_case['input'][:100]}{'...' if len(test_case['input']) > 100 else ''}")
        
        # Old behavior
        old_result = old_behavior(test_case['input'])
        print(f"\n🔴 Old Behavior Result:")
        print(f"Length: {len(old_result)}")
        print(f"Result: {old_result[:200]}{'...' if len(old_result) > 200 else ''}")
        
        try:
            old_parsed = json.loads(old_result)
            print(f"✅ Old Parse Success: {type(old_parsed)}, Items: {len(old_parsed) if isinstance(old_parsed, list) else 1}")
            
            # Check for nested arrays
            if isinstance(old_parsed, list) and len(old_parsed) > 0 and isinstance(old_parsed[0], list):
                print("⚠️  WARNING: Nested array detected in old behavior!")
        except json.JSONDecodeError as e:
            print(f"❌ Old Parse Failed: {e}")
        
        # New behavior  
        new_result = new_behavior(test_case['input'])
        print(f"\n🟢 New Behavior Result:")
        print(f"Length: {len(new_result)}")
        print(f"Result: {new_result[:200]}{'...' if len(new_result) > 200 else ''}")
        
        try:
            new_parsed = json.loads(new_result)
            print(f"✅ New Parse Success: {type(new_parsed)}, Items: {len(new_parsed) if isinstance(new_parsed, list) else 1}")
            
            # Check for nested arrays
            if isinstance(new_parsed, list) and len(new_parsed) > 0 and isinstance(new_parsed[0], list):
                print("⚠️  WARNING: Nested array detected in new behavior!")
            else:
                print("✅ No nested arrays - Good!")
        except json.JSONDecodeError as e:
            print(f"❌ New Parse Failed: {e}")
        
        print(f"\n{'='*80}\n")

if __name__ == "__main__":
    print("🔍 Comparing Old vs New JSON Handling Behavior\n")
    compare_behaviors()
    print("✅ Comparison completed!")
