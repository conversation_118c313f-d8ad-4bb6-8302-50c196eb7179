# ============================================================================
# CUSTOM EMBEDDINGS CLASS
# ============================================================================

import requests
from typing import List


class CustomEmbedding:
    """
    Custom embedding class that calls the embedding API directly
    """

    def __init__(self, model: str, api_base: str, api_key: str):
        self.model = model
        self.api_base = api_base.rstrip('/')  # Remove trailing slash
        self.api_key = api_key

    def embed_query(self, text: str) -> List[float]:
        """
        Create embedding for a single text query

        Args:
            text (str): Text to embed

        Returns:
            List[float]: Embedding vector
        """
        return self._create_embedding(text)

    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """
        Create embeddings for multiple documents

        Args:
            texts (List[str]): List of texts to embed

        Returns:
            List[List[float]]: List of embedding vectors
        """
        return [self._create_embedding(text) for text in texts]

    def _create_embedding(self, text: str) -> List[float]:
        """
        Internal method to create embedding via API call

        Args:
            text (str): Text to embed

        Returns:
            List[float]: Embedding vector
        """
        try:
            # Prepare the request - handle API base URL properly
            api_base = self.api_base.rstrip('/')

            # Check if API base already ends with /v1, if so use /embeddings, otherwise use /v1/embeddings
            if api_base.endswith('/v1'):
                url = f"{api_base}/embeddings"
            else:
                url = f"{api_base}/v1/embeddings"

            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }

            # Prepare the payload - try different formats
            payload = {
                "model": self.model,
                "input": text  # Standard OpenAI format
            }

            print(f"🔗 Calling embedding API: {url}")
            print(f"📝 Payload: {payload}")

            response = requests.post(url, headers=headers, json=payload, timeout=30)

            print(f"📊 Response status: {response.status_code}")

            if response.status_code == 200:
                result = response.json()
                print(f"✅ API response successful")

                # Extract embedding from response
                if 'data' in result and len(result['data']) > 0:
                    embedding = result['data'][0]['embedding']
                    print(f"🎯 Embedding dimension: {len(embedding)}")
                    return embedding
                else:
                    raise Exception(f"Unexpected response format: {result}")
            else:
                # Try alternative payload format if first attempt fails
                if 'input' in payload:
                    print(f"⚠️  First attempt failed, trying alternative format...")

                    # Try with 'text' instead of 'input'
                    alt_payload = {
                        "model": self.model,
                        "text": text
                    }

                    print(f"📝 Alternative payload: {alt_payload}")

                    alt_response = requests.post(url, headers=headers, json=alt_payload, timeout=30)
                    print(f"📊 Alternative response status: {alt_response.status_code}")

                    if alt_response.status_code == 200:
                        result = alt_response.json()
                        print(f"✅ Alternative API response successful")

                        # Extract embedding from response
                        if 'data' in result and len(result['data']) > 0:
                            embedding = result['data'][0]['embedding']
                            print(f"🎯 Embedding dimension: {len(embedding)}")
                            return embedding
                        elif 'embedding' in result:
                            embedding = result['embedding']
                            print(f"🎯 Embedding dimension: {len(embedding)}")
                            return embedding
                        else:
                            raise Exception(f"Unexpected alternative response format: {result}")
                    else:
                        error_text = alt_response.text
                        raise Exception(f"Alternative API call failed with status {alt_response.status_code}: {error_text}")
                else:
                    error_text = response.text
                    raise Exception(f"API call failed with status {response.status_code}: {error_text}")

        except requests.exceptions.RequestException as e:
            raise Exception(f"Network error calling embedding API: {str(e)}")
        except Exception as e:
            raise Exception(f"Error creating embedding: {str(e)}")
