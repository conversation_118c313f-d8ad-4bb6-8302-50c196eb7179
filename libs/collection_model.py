"""
Collection Model - Quản lý collections trong MySQL

Tác g<PERSON>: AI Assistant
<PERSON><PERSON><PERSON>: 2025-07-07
"""

from typing import Dict, List, Optional, Any
from datetime import datetime
import logging
from libs.database_manager import DatabaseManager, create_database_config_from_env

logger = logging.getLogger(__name__)


class Collection:
    """
    Model để làm việc với bảng collections trong MySQL
    """

    def __init__(self, db_manager: DatabaseManager = None):
        """
        Khởi tạo Collection model

        Args:
            db_manager (DatabaseManager): Instance của DatabaseManager
        """
        if db_manager is None:
            # Tạo DatabaseManager với cấu hình từ environment variables
            config = create_database_config_from_env()
            self.db_manager = DatabaseManager(config)
        else:
            self.db_manager = db_manager
    
    def get_by_name(self, name: str) -> Optional[Dict[str, Any]]:
        """
        Lấy collection theo tên

        Args:
            name (str): Tên collection

        Returns:
            Optional[Dict]: Thông tin collection hoặc None
        """
        return self.db_manager.get_collection_by_name(name)

    def get_by_display_name(self, display_name: str) -> Optional[Dict[str, Any]]:
        """
        Lấy collection theo display_name

        Args:
            display_name (str): Tên hiển thị của collection

        Returns:
            Optional[Dict]: Thông tin collection hoặc None
        """
        if not self.db_manager.connection or not self.db_manager.connection.is_connected():
            if not self.db_manager.connect():
                return None

        try:
            cursor = self.db_manager.connection.cursor(dictionary=True)

            query = "SELECT * FROM collections WHERE display_name = %s AND is_active = 1"
            cursor.execute(query, (display_name,))
            result = cursor.fetchone()

            cursor.close()
            return result

        except Exception as e:
            logger.error(f"❌ Lỗi lấy collection theo display_name '{display_name}': {e}")
            return None
    
    def get_all(self, active_only: bool = True) -> List[Dict[str, Any]]:
        """
        Lấy tất cả collections

        Args:
            active_only (bool): Chỉ lấy collections đang hoạt động

        Returns:
            List[Dict]: Danh sách collections
        """
        if active_only:
            return self.db_manager.get_all_collections()

        # Lấy tất cả collections kể cả inactive
        if not self.db_manager.connection or not self.db_manager.connection.is_connected():
            if not self.db_manager.connect():
                return []

        try:
            cursor = self.db_manager.connection.cursor(dictionary=True)

            query = "SELECT * FROM collections ORDER BY name"
            cursor.execute(query)
            results = cursor.fetchall()

            cursor.close()
            return results

        except Exception as e:
            logger.error(f"❌ Lỗi lấy tất cả collections: {e}")
            return []
    
    def create(self, name: str, display_name: str, description: str = None) -> bool:
        """
        Tạo collection mới

        Args:
            name (str): Tên collection (unique key)
            display_name (str): Tên hiển thị
            description (str): Mô tả collection

        Returns:
            bool: True nếu thành công
        """
        return self.db_manager.create_collection(name, display_name, description)
    
    def exists(self, name: str) -> bool:
        """
        Kiểm tra collection có tồn tại và đang hoạt động không

        Args:
            name (str): Tên collection

        Returns:
            bool: True nếu tồn tại và đang hoạt động
        """
        return self.db_manager.collection_exists(name)
    
    def update(self, name: str, display_name: str = None, description: str = None, is_active: bool = None) -> bool:
        """
        Cập nhật thông tin collection

        Args:
            name (str): Tên collection
            display_name (str): Tên hiển thị mới
            description (str): Mô tả mới
            is_active (bool): Trạng thái hoạt động

        Returns:
            bool: True nếu thành công
        """
        if not self.db_manager.connection or not self.db_manager.connection.is_connected():
            if not self.db_manager.connect():
                return False

        try:
            cursor = self.db_manager.connection.cursor()

            # Tạo query động dựa trên tham số
            updates = []
            params = []

            if display_name is not None:
                updates.append("display_name = %s")
                params.append(display_name)

            if description is not None:
                updates.append("description = %s")
                params.append(description)

            if is_active is not None:
                updates.append("is_active = %s")
                params.append(is_active)

            if not updates:
                logger.warning("Không có thông tin nào để cập nhật")
                return False

            updates.append("updated_at = NOW()")
            params.append(name)

            query = f"UPDATE collections SET {', '.join(updates)} WHERE name = %s"
            cursor.execute(query, params)
            self.db_manager.connection.commit()

            affected_rows = cursor.rowcount
            cursor.close()

            if affected_rows > 0:
                logger.info(f"✅ Đã cập nhật collection '{name}'")
                return True
            else:
                logger.warning(f"⚠️ Không tìm thấy collection '{name}' để cập nhật")
                return False

        except Exception as e:
            logger.error(f"❌ Lỗi cập nhật collection '{name}': {e}")
            return False
    
    def delete(self, name: str, soft_delete: bool = True) -> bool:
        """
        Xóa collection
        
        Args:
            name (str): Tên collection
            soft_delete (bool): Xóa mềm (set is_active=False) hay xóa cứng
            
        Returns:
            bool: True nếu thành công
        """
        if soft_delete:
            return self.update(name, is_active=False)
        
        # Xóa cứng
        if not self.db_manager.connection or not self.db_manager.connection.is_connected():
            if not self.db_manager.connect():
                return False
        
        try:
            cursor = self.db_manager.connection.cursor()
            
            query = "DELETE FROM collections WHERE name = %s"
            cursor.execute(query, (name,))
            self.db_manager.connection.commit()
            
            affected_rows = cursor.rowcount
            cursor.close()
            
            if affected_rows > 0:
                logger.info(f"✅ Đã xóa collection '{name}'")
                return True
            else:
                logger.warning(f"⚠️ Không tìm thấy collection '{name}' để xóa")
                return False
                
        except Exception as e:
            logger.error(f"❌ Lỗi xóa collection '{name}': {e}")
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Lấy thống kê về collections
        
        Returns:
            Dict: Thống kê collections
        """
        if not self.db_manager.connection or not self.db_manager.connection.is_connected():
            if not self.db_manager.connect():
                return {"error": "Không thể kết nối database"}
        
        try:
            cursor = self.db_manager.connection.cursor(dictionary=True)
            
            # Đếm tổng số collections
            cursor.execute("SELECT COUNT(*) as total FROM collections")
            total = cursor.fetchone()['total']
            
            # Đếm collections active
            cursor.execute("SELECT COUNT(*) as active FROM collections WHERE is_active = 1")
            active = cursor.fetchone()['active']

            # Đếm collections inactive
            cursor.execute("SELECT COUNT(*) as inactive FROM collections WHERE is_active = 0")
            inactive = cursor.fetchone()['inactive']
            
            # Lấy collections mới nhất
            cursor.execute("""
                SELECT name, created_at 
                FROM collections 
                ORDER BY created_at DESC 
                LIMIT 5
            """)
            recent = cursor.fetchall()
            
            cursor.close()
            
            return {
                "total_collections": total,
                "active_collections": active,
                "inactive_collections": inactive,
                "recent_collections": recent
            }
            
        except Exception as e:
            logger.error(f"❌ Lỗi lấy thống kê collections: {e}")
            return {"error": str(e)}
    
    def seed_default_collections(self) -> bool:
        """
        Tạo các collections mặc định
        
        Returns:
            bool: True nếu thành công
        """
        default_collections = [
            {
                "name": "default_documents",
                "display_name": "Default Documents",
                "description": "Collection mặc định cho documents"
            },
            {
                "name": "qa_documents",
                "display_name": "Q&A Documents",
                "description": "Collection cho dữ liệu Q&A"
            },
            {
                "name": "test_documents",
                "display_name": "Test Documents",
                "description": "Collection cho testing"
            }
        ]

        success_count = 0
        for collection in default_collections:
            if not self.exists(collection["name"]):
                if self.create(collection["name"], collection["display_name"], collection["description"]):
                    success_count += 1
                    logger.info(f"✅ Đã tạo collection mặc định: {collection['name']}")
            else:
                logger.info(f"ℹ️ Collection '{collection['name']}' đã tồn tại")
                success_count += 1
        
        return success_count == len(default_collections)
