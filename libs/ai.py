# ============================================================================
# IMPORTS AND CONFIGURATION
# ============================================================================

from langchain_community.chat_models import ChatOpenAI
from langchain.prompts import ChatPromptTemplate
from langchain.chains import LL<PERSON>hain
from langchain_experimental.text_splitter import SemanticChunker
from langchain_openai import OpenAIEmbeddings
import os
import docx
from dotenv import load_dotenv
from dotenv import dotenv_values
import warnings

# Import for accurate token counting
try:
    import tiktoken
    TIKTOKEN_AVAILABLE = True
    # Cache encodings to improve performance
    _ENCODING_CACHE = {}
except ImportError:
    TIKTOKEN_AVAILABLE = False
    _ENCODING_CACHE = {}
    print("Warning: tiktoken not installed. Token counting will be less accurate.")

# Import LLM Router
from .llm_router import LLMRouter, create_router_from_env

# Import common utilities
from .common import get_env_var

# Import Gemini
try:
    from langchain_google_genai import ChatGoogleGenerativeAI
    GEMINI_AVAILABLE = True
except ImportError:
    GEMINI_AVAILABLE = False
    print("Warning: langchain_google_genai not installed. Gemini models will not be available.")

# Tắt tất cả warnings
warnings.filterwarnings('ignore')


# ============================================================================
# CONSTANTS
# ============================================================================

# Prompt template để phân tích cấu trúc file Word (đã tối ưu token)
EXTRACT_SECTIONS_PROMPT_TEMPLATE = '''Trích xuất chính xác các phần sau từ văn bản, không thay đổi nội dung: Ngày tháng, Tên hoạt động, Mục đích, Chuẩn bị, Tiến hành.
Nội dung: {input}
Chỉ trả về JSON với các trường: ngay_thang (dd-mm-yyyy), ten_hoat_dong, muc_dich, chuan_bi, tien_hanh. Không giải thích, không markdown, chỉ JSON thuần túy.'''

# Prompt template đầy đủ khi cần thêm hướng dẫn chi tiết
EXTRACT_SECTIONS_PROMPT_TEMPLATE_DETAILED = '''Trích xuất chính xác từng phần nội dung gốc từ văn bản sau,chỉ phân tách và giữ nguyên bản gốc từng phần:Ngày tháng,Tên hoạt động,Mục đích,Chuẩn bị,Tiến hành(Văn bản sẽ có nhiều phần)
Nội dung:"""{input}"""
OUTPUT REQUIREMENTS:
- Return only valid JSON
- No explanatory text
- No prefixes like "Here is the JSON format:"  
- No use markdown formatting like ```json or ```
- No suffixes or additional comments
Chỉ trả về kết quả dạng JSON với các trường: ngay_thang, ten_hoat_dong, muc_dich, chuan_bi, tien_hanh. Mỗi trường phải giữ nguyên nội dung gốc, không được thay đổi, ngay_thang định dạng dd-mm-yyyy, không được tóm tắt, không được bỏ sót bất kỳ chi tiết nào. Bỏ tiêu đề của các phần, chỉ giữ nội dung bên dưới. Không hiển thị <think>. Chỉ xuất ra kết quả JSON cuối cùng.
'''


# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================





def read_docx(file_path):
    """Đọc nội dung file Word và trả về text đã được làm sạch"""
    doc = docx.Document(file_path)
    full_text = []
    
    # Đọc các đoạn văn thông thường, loại bỏ dòng trống dư thừa
    for para in doc.paragraphs:
        text = para.text.strip()
        if text:
            full_text.append(text)
    
    # Đọc nội dung trong bảng (nếu có), mỗi dòng là 1 hàng bảng, cell cách nhau bằng tab
    for table in doc.tables:
        for row in table.rows:
            row_text = []
            for cell in row.cells:
                cell_text = cell.text.strip()
                if cell_text:
                    row_text.append(cell_text)
            if row_text:
                full_text.append('\t'.join(row_text))
    
    # Loại bỏ các dòng trống liên tiếp, chỉ giữ 1 dòng trống giữa các đoạn
    cleaned = []
    prev_blank = False
    for line in full_text:
        if not line.strip():
            if not prev_blank:
                cleaned.append('')
            prev_blank = True
        else:
            cleaned.append(line)
            prev_blank = False
    
    return '\n'.join(cleaned)


# ============================================================================
# TOKEN COUNTING FUNCTIONS
# ============================================================================


def count_tokens(text, model_name=None):
    """
    Tính số tokens chính xác sử dụng tiktoken cho các model OpenAI
    và fallback về ước tính cho các model khác
    
    Args:
        text (str): Văn bản cần tính token
        model_name (str): Tên model để chọn encoding phù hợp
        
    Returns:
        int: Số tokens
    """
    if not TIKTOKEN_AVAILABLE:
        # Fallback về phương pháp ước tính cũ
        return len(text) // 3
    
    try:
        # Xác định encoding dựa trên model name
        if model_name:
            # Map các model phổ biến với encoding tương ứng
            model_encoding_map = {
                # OpenAI GPT models
                'gpt-4': 'cl100k_base',
                'gpt-4-turbo': 'cl100k_base', 
                'gpt-4o': 'cl100k_base',
                'gpt-4o-mini': 'cl100k_base',
                'gpt-3.5-turbo': 'cl100k_base',
                'text-embedding-ada-002': 'cl100k_base',
                'text-embedding-3-small': 'cl100k_base',
                'text-embedding-3-large': 'cl100k_base',
                
                # GitHub Models (OpenAI compatible)
                'openai/gpt-4.1': 'cl100k_base',
                'openai/gpt-4.1-mini': 'cl100k_base',
                
                # Groq models (approximate with closest)
                'llama-3.3-70b-versatile': 'cl100k_base',
                'meta-llama/llama-4-maverick-17b-128e-instruct': 'cl100k_base',
                
                # Mistral models (approximate with cl100k_base)
                'mistralai/mistral-small-3.2-24b-instruct:free': 'cl100k_base',
                'mistralai/mistral-large-2411': 'cl100k_base',
                'mistralai/mistral-7b-instruct': 'cl100k_base',
                
                # Gemini models (approximate)
                'gemini-1.5-pro': 'cl100k_base',
                'gemini-1.5-flash': 'cl100k_base',
                'gemini-2.0-flash-exp': 'cl100k_base',
                'gemini-exp-1206': 'cl100k_base',
            }
            
            # Tìm encoding phù hợp
            encoding_name = model_encoding_map.get(model_name.lower(), 'cl100k_base')
            
            # Cache encoding để tăng tốc
            cache_key = model_name.lower()
            if cache_key not in _ENCODING_CACHE:
                try:
                    # Thử lấy encoding cho model cụ thể trước
                    _ENCODING_CACHE[cache_key] = tiktoken.encoding_for_model(model_name)
                except KeyError:
                    # Nếu không tìm thấy model, dùng encoding mặc định
                    _ENCODING_CACHE[cache_key] = tiktoken.get_encoding(encoding_name)
            
            encoding = _ENCODING_CACHE[cache_key]
        else:
            # Mặc định sử dụng cl100k_base (GPT-4, GPT-3.5-turbo)
            if 'default' not in _ENCODING_CACHE:
                _ENCODING_CACHE['default'] = tiktoken.get_encoding('cl100k_base')
            encoding = _ENCODING_CACHE['default']
        
        # Tính số tokens
        tokens = encoding.encode(text)
        return len(tokens)
        
    except Exception as e:
        print(f"Warning: Error calculating tokens with tiktoken: {e}")
        # Fallback về phương pháp ước tính
        return len(text) // 3


def clear_token_cache():
    """Xóa cache encoding để giải phóng memory"""
    global _ENCODING_CACHE
    _ENCODING_CACHE.clear()
    print("🧹 Token encoding cache cleared")


def get_cache_info():
    """Lấy thông tin về cache hiện tại"""
    if not TIKTOKEN_AVAILABLE:
        return {"available": False, "cached_models": []}
    
    return {
        "available": True,
        "cached_models": list(_ENCODING_CACHE.keys()) if _ENCODING_CACHE else [],
        "cache_size": len(_ENCODING_CACHE)
    }


def ensure_token_limit(chunks, max_tokens=8000, model_name=None):
    """
    Đảm bảo không chunk nào vượt quá giới hạn tokens
    
    Args:
        chunks (list): List các chunks văn bản
        max_tokens (int): Giới hạn tokens tối đa
        model_name (str): Tên model để tính token chính xác
        
    Returns:
        list: Danh sách các chunks đã được điều chỉnh
    """
    result_chunks = []
    
    # Detect provider to apply appropriate token counting
    provider = detect_provider_from_model(model_name) if model_name else "openai"
    
    # Apply safety margin for non-OpenAI providers
    if provider in ["openrouter", "groq", "gemini", "claude"]:
        # Apply 10% safety margin for non-OpenAI providers
        effective_max_tokens = int(max_tokens * 0.9)
    else:
        effective_max_tokens = max_tokens
    
    print(f"🔍 Token limit enforcement:")
    print(f"  - Model: {model_name}")
    print(f"  - Detected provider: {provider}")
    print(f"  - Max tokens: {max_tokens}")
    print(f"  - Effective max tokens (with safety margin): {effective_max_tokens}")
    
    total_input_chunks = len(chunks)
    for i, chunk in enumerate(chunks):
        token_info = count_tokens_enhanced(chunk, model_name, provider)
        chunk_tokens = token_info['tokens']
        
        print(f"  - Chunk {i+1}/{total_input_chunks}: {chunk_tokens} tokens ({token_info['provider']}, factor: {token_info['correction_factor']:.3f})")
        
        if chunk_tokens <= effective_max_tokens:
            result_chunks.append(chunk)
        else:
            print(f"    ⚠️  Chunk vượt quá giới hạn, cần chia nhỏ...")
            # Chia nhỏ chunk lớn thành các phần nhỏ hơn
            lines = chunk.split('\n')
            current_chunk = ""
            
            for line in lines:
                test_chunk = current_chunk + '\n' + line if current_chunk else line
                test_token_info = count_tokens_enhanced(test_chunk, model_name, provider)
                if test_token_info['tokens'] <= effective_max_tokens:
                    current_chunk = test_chunk
                else:
                    if current_chunk:
                        result_chunks.append(current_chunk)
                        current_chunk_info = count_tokens_enhanced(current_chunk, model_name, provider)
                        print(f"    ✅ Tạo sub-chunk: {current_chunk_info['tokens']} tokens")
                    current_chunk = line
                    
                    # Nếu một dòng duy nhất vẫn quá dài, cắt theo từ
                    line_token_info = count_tokens_enhanced(current_chunk, model_name, provider)
                    if line_token_info['tokens'] > effective_max_tokens:
                        words = current_chunk.split()
                        temp_chunk = ""
                        for word in words:
                            test_word_chunk = temp_chunk + ' ' + word if temp_chunk else word
                            word_token_info = count_tokens_enhanced(test_word_chunk, model_name, provider)
                            if word_token_info['tokens'] <= effective_max_tokens:
                                temp_chunk = test_word_chunk
                            else:
                                if temp_chunk:
                                    result_chunks.append(temp_chunk)
                                    temp_chunk_info = count_tokens_enhanced(temp_chunk, model_name, provider)
                                    print(f"    ✅ Tạo word-split sub-chunk: {temp_chunk_info['tokens']} tokens")
                                temp_chunk = word
                        if temp_chunk:
                            current_chunk = temp_chunk
                        else:
                            current_chunk = ""
            
            if current_chunk:
                result_chunks.append(current_chunk)
                final_chunk_info = count_tokens_enhanced(current_chunk, model_name, provider)
                print(f"    ✅ Tạo final sub-chunk: {final_chunk_info['tokens']} tokens")
    
    print(f"📊 Kết quả chunking: {total_input_chunks} chunks gốc → {len(result_chunks)} chunks cuối cùng")
    return result_chunks


# ============================================================================
# MAIN PROCESSING FUNCTIONS
# ============================================================================


def extract_sections_from_docx(word_file_path, max_tokens=None, verbose=False, model=None):
    """
    Trích xuất các phần từ file Word sử dụng AI
    
    Args:
        word_file_path (str): Đường dẫn đến file Word
        max_tokens (int): DEPRECATED - Giới hạn tokens sẽ được lấy từ LLM config
        verbose (bool): In thông tin chi tiết quá trình xử lý (mặc định False)
        model (str): Tên model AI để sử dụng (mặc định None sẽ dùng config cũ)
        
    Returns:
        list: Danh sách kết quả JSON từ AI cho từng chunk
        
    Raises:
        Exception: Khi không đọc được file Word hoặc lỗi khi gọi AI
    """
    
    # Load environment variables
    load_dotenv(dotenv_path='.env', override=True)
    
    # Determine which model to use and get max_input_tokens from config
    if model:
        selected_model = model
        print(f"Using selected model: {selected_model}")
        # Try to get max_input_tokens from router config (fallback to default if not available)
        try:
            router = create_router_from_env()
            max_input_tokens = router.get_max_input_tokens()
            if verbose:
                print(f"Max input tokens from router config: {max_input_tokens}")
        except:
            max_input_tokens = max_tokens or 3500  # Fallback to parameter or default
            if verbose:
                print(f"Using fallback max input tokens: {max_input_tokens}")
    else:
        selected_model = get_env_var('OPENAI_MODEL')
        max_input_tokens = max_tokens or 3500  # Use parameter or default for legacy mode
        print(f"Using default model: {selected_model}")
        if verbose:
            print(f"Max input tokens (legacy mode): {max_input_tokens}")
    
    try:
        # Đọc nội dung file Word
        content = read_docx(word_file_path)
        if verbose:
            print(f"Đã đọc được {len(content)} ký tự từ file: {word_file_path}")
            
    except Exception as e:
        raise Exception(f"Không đọc được file Word: {word_file_path}. Lỗi: {e}")

    # Chọn prompt template tối ưu dựa trên độ dài nội dung
    optimized_prompt_template = get_optimized_prompt_template(len(content))
    prompt = ChatPromptTemplate.from_template(optimized_prompt_template)
    
    if verbose:
        prompt_tokens = count_tokens_enhanced(optimized_prompt_template, selected_model)
        full_prompt_tokens = count_tokens_enhanced(EXTRACT_SECTIONS_PROMPT_TEMPLATE_DETAILED, selected_model)
        if prompt_tokens['tokens'] < full_prompt_tokens['tokens']:
            savings = full_prompt_tokens['tokens'] - prompt_tokens['tokens']
            print(f"🔍 Sử dụng prompt tối ưu, tiết kiệm {savings:,} tokens cho mỗi request")
 
    # Khởi tạo LLMChain với model được chọn
    if model:
        llm = create_llm_for_model(model, temperature=float(get_env_var('OPENAI_TEMPERATURE', 0.0)))
    else:
        # Fallback to original configuration
        llm = ChatOpenAI(
            model=get_env_var('OPENAI_MODEL'),
            temperature=float(get_env_var('OPENAI_TEMPERATURE', 0.0)),
            openai_api_base=get_env_var('OPENAI_API_BASE'),
            openai_api_key=get_env_var('OPENAI_API_KEY')
        )
    chain = LLMChain(llm=llm, prompt=prompt)

    try:
        # Tối ưu hóa nội dung trước khi xử lý để giảm token
        optimized_content = optimize_input_text(content)
        if verbose:
            original_tokens = count_tokens_enhanced(content, selected_model)
            optimized_tokens = count_tokens_enhanced(optimized_content, selected_model)
            token_savings = original_tokens['tokens'] - optimized_tokens['tokens']
            savings_percent = (token_savings / original_tokens['tokens']) * 100 if original_tokens['tokens'] > 0 else 0
            print(f"🔍 Token optimization: {token_savings:,} tokens saved ({savings_percent:.1f}%)")
            print(f"  - Original content: {original_tokens['tokens']:,} tokens")
            print(f"  - Optimized content: {optimized_tokens['tokens']:,} tokens")
        
        # Sử dụng phương pháp chunking tối ưu thay vì SemanticChunker khi nội dung lớn
        if len(optimized_content) > 10000:
            # Với nội dung lớn, dùng chunking tối ưu để giảm thiểu token
            provider = detect_provider_from_model(selected_model)
            text_parts = chunk_text_optimally(optimized_content, max_input_tokens, selected_model, provider)
            if verbose:
                print(f"📊 Sử dụng phương pháp chunking tối ưu cho nội dung lớn: {len(text_parts)} chunks")
        else:
            # Với nội dung nhỏ, vẫn dùng SemanticChunker để giữ ngữ nghĩa tốt hơn
            embeddings = OpenAIEmbeddings(
                model=get_env_var('EMBEDDINGS_MODEL'),
                openai_api_base=get_env_var('EMBEDDINGS_API_BASE'),
                openai_api_key=get_env_var('EMBEDDINGS_API_KEY')
            )
            text_splitter = SemanticChunker(
                embeddings=embeddings,
                breakpoint_threshold_type="interquartile",
                breakpoint_threshold_amount=0.05,
                number_of_chunks=None
            )
            initial_chunks = text_splitter.split_text(optimized_content)
            
            # Áp dụng giới hạn tokens cứng với smart counting
            text_parts = ensure_token_limit(initial_chunks, max_tokens=max_input_tokens, model_name=selected_model)
        
        if verbose:
            print(f"Model = {selected_model}")
            print(f"Đã tách thành {len(text_parts)} chunks với giới hạn tokens: {max_input_tokens}")
            for i, part in enumerate(text_parts):
                # Sử dụng enhanced counting để hiển thị thông tin đầy đủ
                token_info = count_tokens_enhanced(part, selected_model)
                print(f"Chunk {i+1}: {token_info['tokens']} tokens ({token_info['provider']}, factor: {token_info['correction_factor']:.2f})")
        
        # Xử lý từng chunk
        all_results = []
        total_input_tokens = 0
        total_output_tokens = 0
        
        for idx, part in enumerate(text_parts):
            if verbose:
                print(f'--- Đang phân tích chunk {idx+1}/{len(text_parts)} ---')
            
            # Tính token input cho chunk này với enhanced counting
            token_info = count_tokens_enhanced(part, selected_model)
            input_tokens = token_info['tokens']  # Sử dụng corrected tokens
            total_input_tokens += input_tokens
            
            if verbose:
                print(f'📊 Chunk {idx+1} Token Info:')
                print(f'  - Input tokens: {input_tokens:,} ({token_info["provider"]}, {token_info["content_type"]})')
                print(f'  - {token_info["accuracy_note"]}')
            
            result = chain.invoke({"input": part})
            
            # Tính token output cho chunk này với enhanced counting
            output_info = count_tokens_enhanced(result['text'], selected_model)
            output_tokens = output_info['tokens']
            total_output_tokens += output_tokens
            
            if verbose:
                print(f'  - Output tokens: {output_tokens:,}')
                print(f'  - Chunk tokens: {input_tokens + output_tokens:,}')
            
            # Thử parse JSON từ kết quả AI
            try:
                import json
                # Làm sạch response để loại bỏ markdown formatting
                cleaned_json = clean_json_response(result['text'])
                print(f"Chunk {idx+1} đã xử lý: {cleaned_json}")  # In 100 ký tự đầu tiên của kết quả
                print(f"---------------------")
                #print(cleaned_json)
                # Thử parse để kiểm tra nội dung trước khi quyết định thêm []
                if not cleaned_json.strip().startswith('['):
                    # Thử parse trực tiếp để xem có phải là single object hay multiple objects
                    try:
                        # Thử parse như single object trước
                        test_parse = json.loads(cleaned_json)
                        # Nếu parse thành công và là single object thì giữ nguyên
                        parsed_result = test_parse
                    except json.JSONDecodeError:
                        # Nếu không parse được, có thể là multiple objects, thử thêm []
                        cleaned_json = "[" + cleaned_json + "]"
                        parsed_result = json.loads(cleaned_json)
                else:
                    # Nếu đã là array, parse trực tiếp
                    parsed_result = json.loads(cleaned_json)
                
                # print(cleaned_json if cleaned_json.strip().startswith('[') else json.dumps(parsed_result, ensure_ascii=False))
                #print(parsed_result)
                # Xử lý kết quả - dù là single object hay array, đều coi như 1 chunk result  
                all_results.append({
                    "chunk_index": idx + 1,
                    "success": True,
                    "data": parsed_result,  # Giữ nguyên structure (có thể là single object hoặc array)
                    "input_tokens": input_tokens,
                    "output_tokens": output_tokens,
                })
            except json.JSONDecodeError as e:
                # Nếu không parse được JSON, vẫn trả về raw text
                all_results.append({
                    "chunk_index": idx + 1,
                    "success": False,
                    "error": f"JSON parse error: {str(e)}",
                    "data": None,
                    "raw_text": result['text'],
                    "input_tokens": input_tokens,
                    "output_tokens": output_tokens,
                })
        #print(all_results)
        # Gộp kết quả từ tất cả chunks
        merged_result = merge_chunk_results(all_results, selected_model, max_input_tokens, float(get_env_var('OPENAI_TEMPERATURE', 0.0)), total_input_tokens, total_output_tokens)
        # Trả về dạng list để tương thích với hệ thống cũ
        return [merged_result]
        
    except Exception as e:
        raise Exception(f"Lỗi khi gọi AI để phân tích: {e}")


def clean_json_response(response_text):
    """
    Làm sạch response từ AI để lấy JSON thuần túy
    Loại bỏ markdown formatting, code blocks, etc.
    Xử lý trường hợp có nhiều JSON objects riêng biệt
    
    Args:
        response_text (str): Response text từ AI
        
    Returns:
        str: JSON string đã được làm sạch
    """
    import re
    import json
    
    # Loại bỏ markdown code blocks
    cleaned = response_text.strip()
    
    # Loại bỏ ```json ở đầu và ``` ở cuối
    if cleaned.startswith('```json'):
        cleaned = cleaned[7:]  # Bỏ ```json
    elif cleaned.startswith('```'):
        cleaned = cleaned[3:]   # Bỏ ```
    
    if cleaned.endswith('```'):
        cleaned = cleaned[:-3]  # Bỏ ``` ở cuối
    
    # Trim whitespace
    cleaned = cleaned.strip()
    
    # Thay thế tab characters thật thành spaces để tránh lỗi JSON
    cleaned = cleaned.replace('\t', '  ')
    
    # Loại bỏ các ký tự điều khiển không hợp lệ khác
    cleaned = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', cleaned)
    
    # Kiểm tra xem có phải là nhiều JSON objects riêng biệt không
    # Tìm pattern }\n\n{ hoặc }\n{
    if re.search(r'}\s*\n\s*{', cleaned):
        # Tách thành các JSON objects riêng biệt
        json_objects = []
        
        # Sử dụng regex để tìm các JSON objects
        # Pattern để tìm các cặp dấu ngoặc nhọn cân bằng
        bracket_count = 0
        current_json = ""
        i = 0
        
        while i < len(cleaned):
            char = cleaned[i]
            current_json += char
            
            if char == '{':
                bracket_count += 1
            elif char == '}':
                bracket_count -= 1
                
                # Nếu đã đóng hết ngoặc, nghĩa là hoàn thành một JSON object
                if bracket_count == 0:
                    try:
                        # Thử parse JSON để kiểm tra tính hợp lệ
                        parsed = json.loads(current_json.strip())
                        json_objects.append(parsed)
                        current_json = ""
                    except json.JSONDecodeError:
                        # Nếu không parse được, tiếp tục thêm ký tự
                        pass
            i += 1
        
        # Nếu tìm được nhiều JSON objects, trả về JSON array
        if len(json_objects) > 1:
            return json.dumps(json_objects, ensure_ascii=False)
        elif len(json_objects) == 1:
            # Chỉ trả về single object, không wrap trong array
            return json.dumps(json_objects[0], ensure_ascii=False)
    
    # Nếu không phải nhiều JSON objects, trả về như cũ
    return cleaned


def merge_chunk_results(all_results, model=None, max_input_tokens=None, temperature=None, total_input_tokens=0, total_output_tokens=0):
    """
    Gộp kết quả từ các chunks thành một mảng JSON duy nhất
    
    Args:
        all_results (list): Danh sách kết quả từ các chunks
        model (str): Tên model được sử dụng
        max_input_tokens (int): Giới hạn input tokens được sử dụng (từ LLM config)
        temperature (float): Temperature được sử dụng
        total_input_tokens (int): Tổng số input tokens đã sử dụng
        total_output_tokens (int): Tổng số output tokens đã sử dụng
        
    Returns:
        dict: Kết quả đã được gộp từ tất cả chunks thành một JSON array
    """
    # Tạo mảng chứa tất cả dữ liệu từ các chunks thành công
    merged_data = []
    successful_chunks = []
    failed_chunks = []
    chunk_details = []
    
    # Phân loại chunks thành công và thất bại
    for result in all_results:
        # Thu thập thông tin chi tiết token của từng chunk
        chunk_detail = {
            "chunk_index": result.get('chunk_index', 0),
            "success": result.get('success', False),
            "input_tokens": result.get('input_tokens', 0),
            "output_tokens": result.get('output_tokens', 0),
            "total_tokens": result.get('input_tokens', 0) + result.get('output_tokens', 0)
        }
        
        if result.get('success', False):
            successful_chunks.append(result)
            # Thêm dữ liệu vào mảng tổng hợp - xử lý cả array và single object
            data = result.get('data', {})
            if data:
                if isinstance(data, list):
                    # Nếu data là array, thêm từng item
                    merged_data.extend(data)
                else:
                    # Nếu data là single object, thêm object đó
                    merged_data.append(data)
        else:
            failed_chunks.append(result)
            chunk_detail["error"] = result.get('error', 'Unknown error')
        
        chunk_details.append(chunk_detail)
    
    # Trả về kết quả cuối cùng với tất cả dữ liệu trong một mảng
    final_result = {
        "success": len(successful_chunks) > 0,
        "total_chunks": len(all_results),
        "successful_chunks": len(successful_chunks),
        "failed_chunks": len(failed_chunks),
        "model": model,
        "max_input_tokens": max_input_tokens,
        "temperature": temperature,
        "total_input_tokens": total_input_tokens,
        "total_output_tokens": total_output_tokens,
        "total_tokens": total_input_tokens + total_output_tokens,
        #"chunk_details": chunk_details,
        "data": merged_data if len(successful_chunks) > 0 else []
    }
    
    if failed_chunks:
        final_result["errors"] = [chunk.get("error", "Unknown error") for chunk in failed_chunks]
    
    return final_result


# ============================================================================
# MODEL MANAGEMENT FUNCTIONS
# ============================================================================

def get_available_models():
    """Get available models grouped by provider"""
    github_models = get_env_var('GITHUB_MODELS', 'openai/gpt-4.1,openai/gpt-4.1-mini').split(',')
    groq_models = get_env_var('GROQ_MODELS', 'llama-3.3-70b-versatile,meta-llama/llama-4-maverick-17b-128e-instruct').split(',')
    localai_models = get_env_var('LOCALAI_MODELS', '').split(',')
    gemini_models = get_env_var('GEMINI_MODELS').split(',')
    
    models = {
        'github': [model.strip() for model in github_models if model.strip()],
        'groq': [model.strip() for model in groq_models if model.strip()],
        'localai': [model.strip() for model in localai_models if model.strip()],
        'gemini': [model.strip() for model in gemini_models if model.strip()]
    }
    
    return models


def create_llm_for_model(model_name, temperature=0.0):
    """
    Create appropriate LLM instance based on model name
    
    Args:
        model_name (str): Name of the model to use
        temperature (float): Temperature for the model
        
    Returns:
        ChatOpenAI or ChatGoogleGenerativeAI: Configured LLM instance
        
    Raises:
        Exception: If model is not supported
    """
    available_models = get_available_models()
    
    # Determine provider based on model name
    if model_name in available_models['github']:
        # Use GitHub provider
        return ChatOpenAI(
            model=model_name,
            temperature=temperature,
            openai_api_base=get_env_var('GITHUB_OPENAI_API_BASE'),
            openai_api_key=get_env_var('GITHUB_OPENAI_API_KEY')
        )
    elif model_name in available_models['groq']:
        # Use Groq provider
        return ChatOpenAI(
            model=model_name,
            temperature=temperature,
            openai_api_base=get_env_var('GROQ_API_BASE'),
            openai_api_key=get_env_var('GROQ_API_KEY')
        )
    elif model_name in available_models['localai']:
        # Use LocalAI provider
        return ChatOpenAI(
            model=model_name,
            temperature=temperature,
            openai_api_base=get_env_var('LOCALAI_API_BASE'),
            openai_api_key=get_env_var('LOCALAI_API_KEY')
        )
    elif model_name in available_models['gemini']:
        # Use Gemini provider
        api_key = get_env_var('GEMINI_API_KEY')
        if not api_key:
            raise Exception("GEMINI_API_KEY environment variable is required for Gemini models")
        
        return ChatGoogleGenerativeAI(
            model=model_name,
            temperature=temperature,
            google_api_key=api_key,
            convert_system_message_to_human=True  # Gemini doesn't support system messages
        )
    else:
        # Fallback to original configuration
        return ChatOpenAI(
            model=get_env_var('OPENAI_MODEL', model_name),
            temperature=temperature,
            openai_api_base=get_env_var('OPENAI_API_BASE'),
            openai_api_key=get_env_var('OPENAI_API_KEY')
        )


# ============================================================================
# MAIN EXECUTION FUNCTIONS
# ============================================================================

# Function để sử dụng trực tiếp như script cũ
def main(word_file_path=None, model=None):
    """
    Function chính để chạy như script gốc
    
    Args:
        word_file_path (str): Đường dẫn file Word, nếu None sẽ dùng 'input.docx'
        model (str): Tên model AI để sử dụng
    """
    if word_file_path is None:
        word_file_path = 'input.docx'
    
    try:
        results = extract_sections_from_docx(word_file_path, verbose=True, model=model)
        result = results[0] if results else {}  # Lấy kết quả đầu tiên từ list
        print("\n\nKết quả cuối cùng:")
        print(f"Model: {result.get('model', 'N/A')}")
        print(f"Max input tokens: {result.get('max_input_tokens', 'N/A')}")
        print(f"Temperature: {result.get('temperature', 'N/A')}")
        print(f"Tổng chunks: {result.get('total_chunks', 0)}")
        print(f"Chunks thành công: {result.get('successful_chunks', 0)}")
        print(f"Chunks thất bại: {result.get('failed_chunks', 0)}")
        print(f"📊 THỐNG KÊ TOKENS:")
        print(f"  - Input tokens: {result.get('total_input_tokens', 0):,}")
        print(f"  - Output tokens: {result.get('total_output_tokens', 0):,}")
        print(f"  - Tổng tokens: {result.get('total_tokens', 0):,}")
        
        if result.get('success', False):
            print("\n✅ KẾT QUẢ TỔNG HỢP:")
            data_array = result.get('data', [])
            if data_array:
                print(f"Tổng cộng {len(data_array)} mục dữ liệu:")
                for i, data in enumerate(data_array, 1):
                    print(f"\n--- MỤC {i} ---")
                    for key, value in data.items():
                        if value:  # Chỉ hiển thị trường có nội dung
                            print(f"{key.upper()}: {value}")
            else:
                print("Không có dữ liệu")
        else:
            print("\n❌ KHÔNG CÓ KẾT QUẢ THÀNH CÔNG")
            if 'errors' in result:
                print("Lỗi:")
                for error in result.get('errors', []):
                    print(f"- {error}")
    except Exception as e:
        print(f"Lỗi: {e}")


# ============================================================================
# ROUTER-BASED PROCESSING FUNCTIONS
# ============================================================================

def extract_sections_from_docx_with_router(word_file_path, max_tokens=None, verbose=False, router=None):
    """
    Trích xuất các phần từ file Word sử dụng LLM Router
    
    Args:
        word_file_path (str): Đường dẫn đến file Word
        max_tokens (int): DEPRECATED - Giới hạn tokens sẽ được lấy từ Router config
        verbose (bool): In thông tin chi tiết quá trình xử lý (mặc định False)
        router (LLMRouter): Router để quản lý LLM, nếu None sẽ tạo mới
        
    Returns:
        list: Danh sách kết quả JSON từ AI cho từng chunk
        
    Raises:
        Exception: Khi không đọc được file Word hoặc lỗi khi gọi AI
    """
    
    # Khởi tạo router nếu chưa có
    if router is None:
        router = create_router_from_env()
        if verbose:
            print("🤖 Đã tạo LLM Router mới")
            router.print_status()
    
    # Lấy max_input_tokens từ router config
    max_input_tokens = router.get_max_input_tokens()
    if verbose:
        print(f"Max input tokens from router: {max_input_tokens}")
    
    # Deprecated warning
    if max_tokens is not None:
        print(f"⚠️  Warning: max_tokens parameter is deprecated. Using max_input_tokens from LLM config: {max_input_tokens}")
    
    try:
        # Đọc nội dung file Word
        content = read_docx(word_file_path)
        if verbose:
            print(f"Đã đọc được {len(content)} ký tự từ file: {word_file_path}")
            
    except Exception as e:
        raise Exception(f"Không đọc được file Word: {word_file_path}. Lỗi: {e}")

    try:
        # Get the actual model being used by the router for better token counting
        available_llms = router.get_available_llms()
        actual_model = available_llms[0].model if available_llms else "gpt-4"
        actual_provider = detect_provider_from_model(actual_model)
        
        # Tối ưu hóa nội dung trước khi xử lý để giảm token
        optimized_content = optimize_input_text(content)
        if verbose:
            original_tokens = count_tokens_enhanced(content, actual_model, actual_provider)
            optimized_tokens = count_tokens_enhanced(optimized_content, actual_model, actual_provider)
            token_savings = original_tokens['tokens'] - optimized_tokens['tokens']
            savings_percent = (token_savings / original_tokens['tokens']) * 100 if original_tokens['tokens'] > 0 else 0
            print(f"🔍 Token optimization: {token_savings:,} tokens saved ({savings_percent:.1f}%)")
            print(f"  - Original content: {original_tokens['tokens']:,} tokens")
            print(f"  - Optimized content: {optimized_tokens['tokens']:,} tokens")
        
        # Sử dụng phương pháp chunking tối ưu thay vì SemanticChunker khi nội dung lớn
        if len(optimized_content) > 10000:
            # Với nội dung lớn, dùng chunking tối ưu để giảm thiểu token
            text_parts = chunk_text_optimally(optimized_content, max_input_tokens, actual_model, actual_provider)
            if verbose:
                print(f"📊 Sử dụng phương pháp chunking tối ưu cho nội dung lớn: {len(text_parts)} chunks")
        else:
            # Với nội dung nhỏ, vẫn dùng SemanticChunker để giữ ngữ nghĩa tốt hơn
            embeddings = OpenAIEmbeddings(
                model=get_env_var('EMBEDDINGS_MODEL'),
                openai_api_base=get_env_var('EMBEDDINGS_API_BASE'),
                openai_api_key=get_env_var('EMBEDDINGS_API_KEY')
            )
            text_splitter = SemanticChunker(
                embeddings=embeddings,
                breakpoint_threshold_type="interquartile",
                breakpoint_threshold_amount=0.05,
                number_of_chunks=None
            )
            initial_chunks = text_splitter.split_text(optimized_content)
            
            # Áp dụng giới hạn tokens cứng với model thực tế
            text_parts = ensure_token_limit(initial_chunks, max_tokens=max_input_tokens, model_name=actual_model)
        
        if verbose:
            print(f"Đã tách thành {len(text_parts)} chunks với giới hạn tokens: {max_input_tokens}")
            print(f"Sử dụng model để ước tính tokens: {actual_model}")
            for i, part in enumerate(text_parts):
                token_info = count_tokens_enhanced(part, actual_model)
                print(f"Chunk {i+1}: {token_info['tokens']} tokens ({token_info['provider']}, factor: {token_info['correction_factor']:.2f})")
        
        # Xử lý từng chunk với router
        all_results = []
        total_input_tokens = 0
        total_output_tokens = 0
        
        for idx, part in enumerate(text_parts):
            if verbose:
                print(f'--- Đang phân tích chunk {idx+1}/{len(text_parts)} với Router ---')
            
            # Tính token input cho chunk này với enhanced counting
            # Sử dụng actual_model thay vì default "gpt-4"
            token_info = count_tokens_enhanced(part, actual_model)
            input_tokens = token_info['tokens']
            total_input_tokens += input_tokens
            
            if verbose:
                print(f'📊 Chunk {idx+1} Token Info:')
                print(f'  - Input tokens: {input_tokens:,} ({token_info["provider"]}, factor: {token_info["correction_factor"]:.3f})')
            
            # Chọn prompt template tối ưu dựa trên độ dài nội dung
            optimized_prompt_template = get_optimized_prompt_template(len(part))
            
            # Sử dụng router để gửi request với prompt tối ưu
            result = router.make_request(optimized_prompt_template, input=part)
            
            if not result:
                # Nếu router không có LLM khả dụng, fallback về method cũ
                if verbose:
                    print("⚠️  Router không có LLM khả dụng, fallback về method cũ")
                return extract_sections_from_docx(word_file_path, max_input_tokens, verbose)
            
            if result.get('success', False):
                response_text = result['response']
                
                # Tính token output cho chunk này - sử dụng model thực tế nếu có
                actual_model = result.get('model', 'gpt-4')
                actual_provider = result.get('provider', 'openrouter')
                output_info = count_tokens_enhanced(response_text, actual_model, actual_provider)
                output_tokens = output_info['tokens']
                total_output_tokens += output_tokens
                
                if verbose:
                    print(f'  - Output tokens: {output_tokens:,}')
                    print(f'  - Chunk tokens: {input_tokens + output_tokens:,}')
                    print(f'  - Sử dụng LLM: {result["llm_name"]} ({result["provider"]}) - Model: {result.get("model", "Unknown")}')
                
                # Thử parse JSON từ kết quả AI
                try:
                    import json
                    # Làm sạch response để loại bỏ markdown formatting
                    cleaned_json = clean_json_response(response_text)
                    print(f"Chunk {idx+1} đã xử lý: {cleaned_json}...")
                    print(f"---------------------")
                    
                    # Thử parse để kiểm tra nội dung trước khi quyết định thêm []
                    if not cleaned_json.strip().startswith('['):
                        try:
                            test_parse = json.loads(cleaned_json)
                            parsed_result = test_parse
                        except json.JSONDecodeError:
                            cleaned_json = "[" + cleaned_json + "]"
                            parsed_result = json.loads(cleaned_json)
                    else:
                        parsed_result = json.loads(cleaned_json)
                    
                    # Xử lý kết quả - dù là single object hay array, đều coi như 1 chunk result
                    all_results.append({
                        "chunk_index": idx + 1,
                        "success": True,
                        "data": parsed_result,  # Giữ nguyên structure (có thể là single object hoặc array)
                        "input_tokens": input_tokens,
                        "output_tokens": output_tokens,
                        "llm_used": result["llm_name"],
                        "provider": result["provider"],
                        "model": result.get("model", "Unknown")
                    })
                except json.JSONDecodeError as e:
                    all_results.append({
                        "chunk_index": idx + 1,
                        "success": False,
                        "error": f"JSON parse error: {str(e)}",
                        "data": None,
                        "raw_text": response_text,
                        "input_tokens": input_tokens,
                        "output_tokens": output_tokens,
                        "llm_used": result["llm_name"],
                        "provider": result["provider"],
                        "model": result.get("model", "Unknown")
                    })
            else:
                # LLM request thất bại
                if verbose:
                    print(f'  - LLM request thất bại: {result.get("error", "Unknown error")}')
                
                all_results.append({
                    "chunk_index": idx + 1,
                    "success": False,
                    "error": result.get('error', 'Unknown error'),
                    "data": None,
                    "input_tokens": input_tokens,
                    "output_tokens": 0,
                    "llm_used": result.get("llm_name", "Unknown"),
                    "provider": result.get("provider", "Unknown"),
                    "model": result.get("model", "Unknown")
                })
        
        # Gộp kết quả từ tất cả chunks
        merged_result = merge_chunk_results_with_router(all_results, router, max_input_tokens, total_input_tokens, total_output_tokens)
        
        # In trạng thái router sau khi xử lý
        if verbose:
            print("\n🤖 TRẠNG THÁI ROUTER SAU KHI XỬ LÝ:")
            router.print_status()
        
        return [merged_result]
        
    except Exception as e:
        raise Exception(f"Lỗi khi gọi AI Router để phân tích: {e}")


def merge_chunk_results_with_router(all_results, router, max_input_tokens=None, total_input_tokens=0, total_output_tokens=0):
    """
    Gộp kết quả từ các chunks với thông tin từ router
    
    Args:
        all_results (list): Danh sách kết quả từ các chunks
        router (LLMRouter): Router đã được sử dụng
        max_input_tokens (int): Giới hạn input tokens từ LLM config
        total_input_tokens (int): Tổng số input tokens đã sử dụng
        total_output_tokens (int): Tổng số output tokens đã sử dụng
        
    Returns:
        dict: Kết quả đã được gộp từ tất cả chunks với thông tin router
    """
    # Tạo mảng chứa tất cả dữ liệu từ các chunks thành công
    merged_data = []
    successful_chunks = []
    failed_chunks = []
    chunk_details = []
    llm_usage = {}  # Theo dõi usage của từng LLM
    
    # Phân loại chunks thành công và thất bại
    for result in all_results:
        llm_name = result.get('llm_used', 'Unknown')
        
        # Thống kê usage theo LLM
        if llm_name not in llm_usage:
            llm_usage[llm_name] = {
                'chunks': 0,
                'successful_chunks': 0,
                'failed_chunks': 0,
                'input_tokens': 0,
                'output_tokens': 0,
                'provider': result.get('provider', 'Unknown'),
                'model': result.get('model', 'Unknown')
            }
        
        # Cập nhật provider và model nếu chưa có hoặc là Unknown
        if llm_usage[llm_name]['provider'] == 'Unknown' and result.get('provider'):
            llm_usage[llm_name]['provider'] = result.get('provider')
        if llm_usage[llm_name]['model'] == 'Unknown' and result.get('model'):
            llm_usage[llm_name]['model'] = result.get('model')
        
        llm_usage[llm_name]['chunks'] += 1
        llm_usage[llm_name]['input_tokens'] += result.get('input_tokens', 0)
        llm_usage[llm_name]['output_tokens'] += result.get('output_tokens', 0)
        
        # Thu thập thông tin chi tiết token của từng chunk
        chunk_detail = {
            "chunk_index": result.get('chunk_index', 0),
            "success": result.get('success', False),
            "input_tokens": result.get('input_tokens', 0),
            "output_tokens": result.get('output_tokens', 0),
            "total_tokens": result.get('input_tokens', 0) + result.get('output_tokens', 0),
            "llm_used": llm_name,
            "provider": result.get('provider', 'Unknown')
        }
        
        if result.get('success', False):
            successful_chunks.append(result)
            llm_usage[llm_name]['successful_chunks'] += 1
            # Thêm dữ liệu vào mảng tổng hợp - xử lý cả array và single object
            data = result.get('data', {})
            if data:
                if isinstance(data, list):
                    # Nếu data là array, thêm từng item
                    merged_data.extend(data)
                else:
                    # Nếu data là single object, thêm object đó
                    merged_data.append(data)
        else:
            failed_chunks.append(result)
            llm_usage[llm_name]['failed_chunks'] += 1
            chunk_detail["error"] = result.get('error', 'Unknown error')
        
        chunk_details.append(chunk_detail)
    
    # Lấy trạng thái router
    router_status = router.get_status()
    
    # Trả về kết quả cuối cùng với tất cả dữ liệu trong một mảng
    final_result = {
        "success": len(successful_chunks) > 0,
        "total_chunks": len(all_results),
        "successful_chunks": len(successful_chunks),
        "failed_chunks": len(failed_chunks),
        "max_input_tokens": max_input_tokens,
        "total_input_tokens": total_input_tokens,
        "total_output_tokens": total_output_tokens,
        "total_tokens": total_input_tokens + total_output_tokens,
        "llm_usage": llm_usage,
        "router_status": {
            "total_llms": router_status['total_llms'],
            "available_llms": router_status['available_llms'],
            "total_remaining_calls": router_status['total_remaining_calls']
        },
        #"chunk_details": chunk_details,
        "data": merged_data if len(successful_chunks) > 0 else []
    }
    
    if failed_chunks:
        final_result["errors"] = [chunk.get("error", "Unknown error") for chunk in failed_chunks]
    
    return final_result


def main_with_router(word_file_path=None, router=None):
    """
    Function chính để chạy với LLM Router
    
    Args:
        word_file_path (str): Đường dẫn file Word, nếu None sẽ dùng 'input.docx'
        router (LLMRouter): LLM Router instance, nếu None sẽ tạo mới
    """
    if word_file_path is None:
        word_file_path = 'input.docx'
    
    if router is None:
        router = create_router_from_env()
        print("🤖 Đã tạo LLM Router:")
        router.print_status()
    
    try:
        results = extract_sections_from_docx_with_router(word_file_path, verbose=True, router=router)
        result = results[0] if results else {}
        
        print("\n\n" + "="*80)
        print("📊 KẾT QUẢ CUỐI CÙNG VỚI LLM ROUTER")
        print("="*80)
        
        # Thông tin cơ bản
        print(f"Max input tokens: {result.get('max_input_tokens', 'N/A')}")
        print(f"Tổng chunks: {result.get('total_chunks', 0)}")
        print(f"Chunks thành công: {result.get('successful_chunks', 0)}")
        print(f"Chunks thất bại: {result.get('failed_chunks', 0)}")
        
        # Thống kê tokens
        print(f"\n📊 THỐNG KÊ TOKENS:")
        print(f"  - Input tokens: {result.get('total_input_tokens', 0):,}")
        print(f"  - Output tokens: {result.get('total_output_tokens', 0):,}")
        print(f"  - Tổng tokens: {result.get('total_tokens', 0):,}")
        
        # Thống kê usage theo LLM
        llm_usage = result.get('llm_usage', {})
        if llm_usage:
            print(f"\n🤖 THỐNG KÊ USAGE THEO LLM:")
            for llm_name, usage in llm_usage.items():
                success_rate = (usage['successful_chunks'] / usage['chunks'] * 100) if usage['chunks'] > 0 else 0
                print(f"\n  🔹 {llm_name} ({usage['provider']}):")
                print(f"     - Model: {usage.get('model', 'N/A')}")
                print(f"     - Chunks xử lý: {usage['chunks']}")
                print(f"     - Thành công: {usage['successful_chunks']} ({success_rate:.1f}%)")
                print(f"     - Thất bại: {usage['failed_chunks']}")
                print(f"     - Input tokens: {usage['input_tokens']:,}")
                print(f"     - Output tokens: {usage['output_tokens']:,}")
                print(f"     - Tổng tokens: {usage['input_tokens'] + usage['output_tokens']:,}")
        
        # Trạng thái router
        router_status = result.get('router_status', {})
        if router_status:
            print(f"\n🔧 TRẠNG THÁI ROUTER:")
            print(f"  - Tổng LLM: {router_status.get('total_llms', 0)}")
            print(f"  - LLM khả dụng: {router_status.get('available_llms', 0)}")
            print(f"  - Tổng lượt call còn lại: {router_status.get('total_remaining_calls', 0):,}")
        
        # Hiển thị dữ liệu kết quả
        if result.get('success', False):
            print("\n✅ KẾT QUẢ TỔNG HỢP:")
            data_array = result.get('data', [])
            if data_array:
                print(f"Tổng cộng {len(data_array)} mục dữ liệu:")
                for i, data in enumerate(data_array, 1):
                    print(f"\n--- MỤC {i} ---")
                    for key, value in data.items():
                        if value:  # Chỉ hiển thị trường có nội dung
                            print(f"{key.upper()}: {value}")
            else:
                print("Không có dữ liệu")
        else:
            print("\n❌ KHÔNG CÓ KẾT QUẢ THÀNH CÔNG")
            if 'errors' in result:
                print("Lỗi:")
                for error in result.get('errors', []):
                    print(f"- {error}")
        
        # Hiển thị trạng thái router cuối cùng
        print(f"\n🤖 TRẠNG THÁI ROUTER SAU KHI HOÀN THÀNH:")
        router.print_status()
        
    except Exception as e:
        print(f"Lỗi: {e}")


# ============================================================================
# ENHANCED TOKEN COUNTING SYSTEM
# ============================================================================

def detect_provider_from_model(model_name):
    """Tự động phát hiện provider từ tên model"""
    if not model_name:
        return "openai"
    
    model_lower = model_name.lower()
    
    # Provider detection patterns
    if model_lower.startswith("openai/"):
        return "github"
    elif model_lower.startswith(("gpt-", "text-embedding")):
        return "openai"
    elif model_lower.startswith(("gemini", "google/")):
        return "gemini"
    elif model_lower.startswith(("claude", "anthropic/")):
        return "claude"
    elif model_lower.startswith("groq/") or "groq" in model_lower:
        return "groq"
    elif any(x in model_lower for x in ["llama", "mixtral", "qwen", "deepseek", "mistral"]):
        return "openrouter"
    elif "/" in model_lower and any(x in model_lower for x in ["mistralai", "meta-llama", "anthropic", "google", "cohere"]):
        return "openrouter"  # Most models with provider/ prefix are from OpenRouter
    else:
        return "openai"  # Default fallback


def get_correction_factor(provider, content_type="mixed"):
    """
    Lấy correction factor dựa trên provider và loại content
    
    Args:
        provider (str): Tên provider
        content_type (str): Loại content (vietnamese, english, code, json, mixed)
    """
    # Base correction factors cho từng provider
    base_factors = {
        "openai": 1.0,      # Baseline - tiktoken accurate
        "github": 0.95,     # GitHub models (OpenAI compatible)
        "openrouter": 0.85, # OpenRouter models (including Mistral, Llama, etc.)
        "groq": 0.90,       # Groq models
        "gemini": 0.82,     # Google Gemini
        "claude": 0.80,     # Anthropic Claude
        "localai": 1.0,     # LocalAI varies
        "mistral": 0.83,    # Mistral models specifically
    }
    
    # Content-specific adjustments
    content_adjustments = {
        "vietnamese": -0.03,  # Vietnamese text typically needs lower factor
        "english": 0.0,       # Baseline
        "code": +0.02,        # Code might need slightly higher
        "json": +0.01,        # JSON might need slightly higher
        "mixed": 0.0,         # Mixed content
    }
    
    base_factor = base_factors.get(provider.lower(), 1.0)
    adjustment = content_adjustments.get(content_type.lower(), 0.0)
    
    return max(0.5, min(1.2, base_factor + adjustment))  # Clamp between 0.5 and 1.2


def count_tokens_enhanced(text, model_name=None, provider=None, content_type="mixed"):
    """
    Enhanced token counting với auto-detection và correction
    
    Args:
        text (str): Văn bản cần tính token
        model_name (str): Tên model (optional)
        provider (str): Provider override (optional)
        content_type (str): Loại content để fine-tune correction
        
    Returns:
        dict: Detailed token information
    """
    # Auto-detect provider nếu không được cung cấp
    if not provider:
        provider = detect_provider_from_model(model_name)
    
    # Auto-detect content type nếu cần (basic heuristics)
    if content_type == "mixed" and text:
        if any(ord(c) > 127 for c in text[:100]):  # Non-ASCII chars
            if any(c in "àáảãạăắằẳẵặâấầẩẫậèéẻẽẹêếềểễệìíỉĩịòóỏõọôốồổỗộơớờởỡợùúủũụưứừửữựỳýỷỹỵđ" for c in text.lower()[:200]):
                content_type = "vietnamese"
        if text.strip().startswith(("{", "[")):
            content_type = "json"
        elif any(keyword in text[:200].lower() for keyword in ["def ", "function", "class ", "import", "from "]):
            content_type = "code"
    
    # Calculate base tokens
    base_tokens = count_tokens(text, model_name)
    
    # Apply correction factor
    correction_factor = get_correction_factor(provider, content_type)
    adjusted_tokens = int(base_tokens * correction_factor)
    
    return {
        "tokens": adjusted_tokens,
        "base_tokens": base_tokens,
        "provider": provider,
        "model": model_name or "unknown",
        "content_type": content_type,
        "correction_factor": correction_factor,
        "accuracy_note": f"Estimated {int((1-abs(1-correction_factor))*100)}% accuracy for {provider}"
    }


def count_tokens_simple(text, model_name=None):
    """
    Simplified token counting - một function duy nhất để rule them all
    
    Args:
        text (str): Văn bản cần tính token
        model_name (str): Tên model (optional)
        
    Returns:
        int: Số tokens đã được tối ưu hóa
    """
    result = count_tokens_enhanced(text, model_name)
    return result["tokens"]


# ============================================================================
# BACKWARD COMPATIBILITY FUNCTIONS (Legacy support)
# ============================================================================

def count_tokens_smart(text, model_name=None, provider=None):
    """Backward compatibility wrapper"""
    result = count_tokens_enhanced(text, model_name, provider)
    return {
        "tokens": result["tokens"],
        "base_tokens": result["base_tokens"],
        "method": "enhanced",
        "provider": result["provider"],
        "correction_factor": result["correction_factor"],
        "model": result["model"]
    }


def count_tokens_with_correction(text, model_name=None, provider="openai"):
    """Backward compatibility wrapper"""
    result = count_tokens_enhanced(text, model_name, provider)
    return result["tokens"]


# ============================================================================
# TOKEN OPTIMIZATION FUNCTIONS
# ============================================================================

def optimize_input_text(text):
    """
    Tối ưu hóa văn bản đầu vào để giảm token:
    - Loại bỏ khoảng trắng dư thừa
    - Thu gọn nhiều dòng trống thành một dòng trống
    - Loại bỏ khoảng trắng đầu/cuối dòng
    - Loại bỏ ký tự đặc biệt không cần thiết
    
    Args:
        text (str): Văn bản gốc
        
    Returns:
        str: Văn bản đã được tối ưu hóa
    """
    import re
    
    # Loại bỏ khoảng trắng đầu/cuối dòng
    lines = [line.strip() for line in text.splitlines()]
    
    # Loại bỏ dòng trống ở đầu và cuối
    while lines and not lines[0]:
        lines.pop(0)
    while lines and not lines[-1]:
        lines.pop()
    
    # Thu gọn nhiều dòng trống thành một dòng trống
    cleaned_lines = []
    prev_blank = False
    for line in lines:
        if not line:
            if not prev_blank:
                cleaned_lines.append("")
            prev_blank = True
        else:
            cleaned_lines.append(line)
            prev_blank = False
    
    # Nối các dòng lại
    cleaned_text = "\n".join(cleaned_lines)
    
    # Loại bỏ ký tự điều khiển không cần thiết
    cleaned_text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', cleaned_text)
    
    # Thay thế nhiều khoảng trắng liên tiếp thành một khoảng trắng
    cleaned_text = re.sub(r' {2,}', ' ', cleaned_text)
    
    return cleaned_text


def chunk_text_optimally(text, max_tokens, model_name=None, provider=None):
    """
    Chia văn bản thành các chunk tối ưu để gửi tới LLM,
    đảm bảo mỗi chunk gần với giới hạn token tối đa nhưng không vượt quá
    
    Args:
        text (str): Văn bản cần chia
        max_tokens (int): Giới hạn token tối đa cho mỗi chunk
        model_name (str): Tên model để tính token chính xác
        provider (str): Provider để áp dụng correction factor phù hợp
        
    Returns:
        list: Danh sách các chunk đã được tối ưu
    """
    # Áp dụng safety margin
    if provider in ["openrouter", "groq", "gemini", "claude"]:
        effective_max_tokens = int(max_tokens * 0.9)
    else:
        effective_max_tokens = max_tokens
    
    # Tối ưu văn bản đầu vào
    text = optimize_input_text(text)
    
    # Chia theo đoạn văn để giữ nguyên ngữ nghĩa
    paragraphs = text.split("\n\n")
    chunks = []
    current_chunk = ""
    
    for para in paragraphs:
        # Kiểm tra nếu đoạn quá dài
        para_info = count_tokens_enhanced(para, model_name, provider)
        if para_info['tokens'] > effective_max_tokens:
            # Nếu một đoạn dài hơn giới hạn, cần chia nhỏ hơn
            if current_chunk:
                chunks.append(current_chunk)
                current_chunk = ""
            
            # Chia đoạn dài thành các dòng
            lines = para.split("\n")
            temp_chunk = ""
            
            for line in lines:
                test_chunk = temp_chunk + "\n" + line if temp_chunk else line
                test_info = count_tokens_enhanced(test_chunk, model_name, provider)
                
                if test_info['tokens'] <= effective_max_tokens:
                    temp_chunk = test_chunk
                else:
                    if temp_chunk:
                        chunks.append(temp_chunk)
                    temp_chunk = line
                    
                    # Kiểm tra nếu một dòng duy nhất vẫn quá dài
                    line_info = count_tokens_enhanced(line, model_name, provider)
                    if line_info['tokens'] > effective_max_tokens:
                        # Chia theo từ nếu cần
                        words = line.split()
                        word_chunk = ""
                        
                        for word in words:
                            test_word = word_chunk + " " + word if word_chunk else word
                            word_info = count_tokens_enhanced(test_word, model_name, provider)
                            
                            if word_info['tokens'] <= effective_max_tokens:
                                word_chunk = test_word
                            else:
                                if word_chunk:
                                    chunks.append(word_chunk)
                                word_chunk = word
                        
                        if word_chunk:
                            temp_chunk = word_chunk
                        else:
                            temp_chunk = ""
            
            if temp_chunk:
                current_chunk = temp_chunk
        else:
            # Kiểm tra nếu thêm đoạn mới vào chunk hiện tại có vượt giới hạn không
            test_chunk = current_chunk + "\n\n" + para if current_chunk else para
            test_info = count_tokens_enhanced(test_chunk, model_name, provider)
            
            if test_info['tokens'] <= effective_max_tokens:
                current_chunk = test_chunk
            else:
                chunks.append(current_chunk)
                current_chunk = para
    
    # Thêm chunk cuối cùng nếu còn
    if current_chunk:
        chunks.append(current_chunk)
    
    # Lọc bỏ các chunk quá ngắn hoặc trống
    result_chunks = [c for c in chunks if c and len(c.strip()) > 10]
    
    return result_chunks


def get_optimized_prompt_template(content_length=None):
    """
    Trả về prompt template tối ưu dựa trên độ dài nội dung
    
    Args:
        content_length (int): Độ dài của nội dung đầu vào
        
    Returns:
        str: Prompt template tối ưu
    """
    # Nếu nội dung ngắn hoặc không xác định, dùng prompt đầy đủ
    if not content_length or content_length < 3000:
        return EXTRACT_SECTIONS_PROMPT_TEMPLATE_DETAILED
    
    # Với nội dung dài, dùng prompt ngắn gọn để tiết kiệm token
    return EXTRACT_SECTIONS_PROMPT_TEMPLATE
