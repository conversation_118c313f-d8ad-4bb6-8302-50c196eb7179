# ============================================================================
# COMMON UTILITIES LIBRARY
# ============================================================================

import os
from dotenv import load_dotenv, dotenv_values


def get_env_var(key, default=None):
    """Get environment variable with fallback to .env file and default value"""
    # First try to load from system environment variables
    value = os.environ.get(key)
    if value is not None:
        return value

    # If not found, try to load from .env file
    # Force reload to get the latest changes
    load_dotenv(dotenv_path='.env', override=True)

    # Try again from environment variables after reloading
    value = os.environ.get(key)
    if value is not None:
        return value

    # If still not found, try from env dictionary
    try:
        env = dotenv_values(".env")
        value = env.get(key)
        if value is not None:
            return value
    except Exception:
        pass

    # Return default value if all else fails
    return default
