#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Request Logger Module
Ghi log các request vào API theo ngày
"""

import os
import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional
from fastapi import Request, Response
from fastapi.responses import JSONResponse
import time
import asyncio
from pathlib import Path


class RequestLogger:
    """Class để ghi log các request vào API"""
    
    def __init__(self, log_dir: str = "logs"):
        """
        Khởi tạo RequestLogger
        
        Args:
            log_dir (str): Thư mục chứa log files
        """
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        
        # Tạo logger cho từng ngày
        self.loggers = {}
        
    def get_daily_logger(self, date_str: str) -> logging.Logger:
        """
        Lấy logger cho ngày cụ thể
        
        Args:
            date_str (str): Ngày theo format YYYY-MM-DD
            
        Returns:
            logging.Logger: Logger cho ngày đó
        """
        if date_str not in self.loggers:
            # Tạo logger mới cho ngày này
            logger = logging.getLogger(f"api_requests_{date_str}")
            logger.setLevel(logging.INFO)
            
            # Xóa handlers cũ nếu có
            for handler in logger.handlers[:]:
                logger.removeHandler(handler)
            
            # Tạo file handler
            log_file = self.log_dir / f"api_requests_{date_str}.log"
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            
            # Tạo formatter
            formatter = logging.Formatter(
                '%(asctime)s | %(levelname)s | %(message)s',
                datefmt='%H:%M:%S'
            )
            file_handler.setFormatter(formatter)
            
            logger.addHandler(file_handler)
            logger.propagate = False  # Không propagate lên root logger
            
            self.loggers[date_str] = logger
            
        return self.loggers[date_str]
    
    def log_request(self, 
                   method: str,
                   url: str, 
                   headers: Dict[str, str],
                   query_params: Dict[str, Any],
                   body: Optional[str] = None,
                   client_ip: str = "unknown",
                   user_agent: str = "unknown"):
        """
        Ghi log request
        
        Args:
            method (str): HTTP method
            url (str): URL được request
            headers (Dict[str, str]): Request headers
            query_params (Dict[str, Any]): Query parameters
            body (Optional[str]): Request body
            client_ip (str): IP của client
            user_agent (str): User agent
        """
        try:
            # Lấy ngày hiện tại
            now = datetime.now()
            date_str = now.strftime("%Y-%m-%d")
            
            # Lấy logger cho ngày này
            logger = self.get_daily_logger(date_str)
            
            # Tạo log entry
            log_entry = {
                "timestamp": now.isoformat(),
                "method": method,
                "url": url,
                "client_ip": client_ip,
                "user_agent": user_agent,
                "query_params": query_params,
                "headers": {k: v for k, v in headers.items() if k.lower() not in ['authorization', 'cookie']},  # Loại bỏ sensitive headers
            }
            
            # Thêm body nếu có và không quá lớn
            if body and len(body) < 10000:  # Giới hạn 10KB
                try:
                    # Thử parse JSON
                    log_entry["body"] = json.loads(body)
                except:
                    log_entry["body"] = body[:1000] + "..." if len(body) > 1000 else body
            elif body:
                log_entry["body"] = f"[BODY TOO LARGE: {len(body)} bytes]"
            
            # Ghi log
            logger.info(f"REQUEST | {json.dumps(log_entry, ensure_ascii=False)}")
            
        except Exception as e:
            # Fallback logging nếu có lỗi
            print(f"Error logging request: {e}")
    
    def log_response(self,
                    method: str,
                    url: str,
                    status_code: int,
                    response_time: float,
                    response_size: int = 0,
                    error: Optional[str] = None):
        """
        Ghi log response
        
        Args:
            method (str): HTTP method
            url (str): URL được request
            status_code (int): HTTP status code
            response_time (float): Thời gian xử lý (seconds)
            response_size (int): Kích thước response (bytes)
            error (Optional[str]): Lỗi nếu có
        """
        try:
            # Lấy ngày hiện tại
            now = datetime.now()
            date_str = now.strftime("%Y-%m-%d")
            
            # Lấy logger cho ngày này
            logger = self.get_daily_logger(date_str)
            
            # Tạo log entry
            log_entry = {
                "timestamp": now.isoformat(),
                "method": method,
                "url": url,
                "status_code": status_code,
                "response_time": round(response_time, 3),
                "response_size": response_size
            }
            
            if error:
                log_entry["error"] = error
            
            # Xác định log level
            if status_code >= 500:
                log_level = "ERROR"
            elif status_code >= 400:
                log_level = "WARNING"
            else:
                log_level = "INFO"
            
            # Ghi log
            logger.log(
                getattr(logging, log_level),
                f"RESPONSE | {json.dumps(log_entry, ensure_ascii=False)}"
            )
            
        except Exception as e:
            # Fallback logging nếu có lỗi
            print(f"Error logging response: {e}")
    
    def cleanup_old_logs(self, days_to_keep: int = 30):
        """
        Xóa log files cũ hơn số ngày chỉ định
        
        Args:
            days_to_keep (int): Số ngày giữ lại log
        """
        try:
            cutoff_date = datetime.now().timestamp() - (days_to_keep * 24 * 60 * 60)
            
            for log_file in self.log_dir.glob("api_requests_*.log"):
                if log_file.stat().st_mtime < cutoff_date:
                    log_file.unlink()
                    print(f"Deleted old log file: {log_file}")
                    
        except Exception as e:
            print(f"Error cleaning up old logs: {e}")


# Global logger instance
request_logger = RequestLogger()


async def log_requests_middleware(request: Request, call_next):
    """
    Middleware để log tất cả requests và responses
    
    Args:
        request (Request): FastAPI request object
        call_next: Next middleware/endpoint
        
    Returns:
        Response: FastAPI response
    """
    start_time = time.time()
    
    # Lấy thông tin request
    method = request.method
    url = str(request.url)
    client_ip = request.client.host if request.client else "unknown"
    user_agent = request.headers.get("user-agent", "unknown")
    headers = dict(request.headers)
    query_params = dict(request.query_params)
    
    # Đọc body (chỉ cho POST/PUT/PATCH)
    body = None
    if method in ["POST", "PUT", "PATCH"]:
        try:
            # Lưu body để có thể đọc lại
            body_bytes = await request.body()
            if body_bytes:
                body = body_bytes.decode('utf-8')
        except Exception as e:
            body = f"[ERROR READING BODY: {e}]"
    
    # Log request
    request_logger.log_request(
        method=method,
        url=url,
        headers=headers,
        query_params=query_params,
        body=body,
        client_ip=client_ip,
        user_agent=user_agent
    )
    
    # Xử lý request
    response = None
    error = None
    try:
        response = await call_next(request)
    except Exception as e:
        error = str(e)
        # Tạo error response
        response = JSONResponse(
            status_code=500,
            content={"error": "Internal server error", "detail": str(e)}
        )
    
    # Tính thời gian xử lý
    process_time = time.time() - start_time
    
    # Lấy kích thước response
    response_size = 0
    if hasattr(response, 'body'):
        response_size = len(response.body) if response.body else 0
    
    # Log response
    request_logger.log_response(
        method=method,
        url=url,
        status_code=response.status_code,
        response_time=process_time,
        response_size=response_size,
        error=error
    )
    
    # Thêm response time vào header
    response.headers["X-Process-Time"] = str(round(process_time, 3))
    
    return response
