"""
Database Manager để quản lý MySQL database cho LLM Router
- <PERSON><PERSON><PERSON> log mỗi lần request
- <PERSON><PERSON><PERSON><PERSON> lý remaining_calls theo ngày
- <PERSON><PERSON> calls hàng ngày
"""

import mysql.connector
from mysql.connector import Error
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Any
import json
import logging
from dataclasses import dataclass

# Cấu hình logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class DatabaseConfig:
    """Cấu hình database"""
    host: str = "localhost"
    port: int = 3306
    database: str = "llm_router"
    user: str = "root"
    password: str = ""
    charset: str = "utf8mb4"

class DatabaseManager:
    """Quản lý database MySQL cho LLM Router"""
    
    def __init__(self, config: DatabaseConfig = None):
        """
        Khởi tạo database manager
        
        Args:
            config: <PERSON><PERSON><PERSON> h<PERSON>nh database, nếu None sẽ dùng config mặc định
        """
        self.config = config or DatabaseConfig()
        self.connection = None
        #self.init_database()
    
    def connect(self):
        """Kết nối đến MySQL database"""
        try:
            self.connection = mysql.connector.connect(
                host=self.config.host,
                port=self.config.port,
                database=self.config.database,
                user=self.config.user,
                password=self.config.password,
                charset=self.config.charset,
                autocommit=True
            )
            logger.info(f"✅ Đã kết nối thành công đến MySQL database: {self.config.database}")
            return True
        except Error as e:
            logger.error(f"❌ Lỗi kết nối database: {e}")
            return False
    
    def disconnect(self):
        """Ngắt kết nối database"""
        if self.connection and self.connection.is_connected():
            self.connection.close()
            logger.info("✅ Đã ngắt kết nối database")
    
    def create_database(self):
        """Tạo database nếu chưa tồn tại"""
        try:
            # Kết nối không chỉ định database để tạo database
            temp_connection = mysql.connector.connect(
                host=self.config.host,
                port=self.config.port,
                user=self.config.user,
                password=self.config.password,
                charset=self.config.charset
            )
            
            cursor = temp_connection.cursor()
            cursor.execute(f"CREATE DATABASE IF NOT EXISTS {self.config.database} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            temp_connection.commit()
            
            cursor.close()
            temp_connection.close()
            
            logger.info(f"✅ Database '{self.config.database}' đã được tạo hoặc đã tồn tại")
            return True
            
        except Error as e:
            logger.error(f"❌ Lỗi tạo database: {e}")
            return False
    
    def create_tables(self):
        """Tạo các bảng cần thiết"""
        if not self.connection or not self.connection.is_connected():
            if not self.connect():
                return False
        
        try:
            cursor = self.connection.cursor()
            
            # Bảng lưu log requests
            create_request_logs_table = """
            CREATE TABLE IF NOT EXISTS request_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                date DATE NOT NULL,
                llm_name VARCHAR(255) NOT NULL,
                provider VARCHAR(100) NOT NULL,
                model VARCHAR(255) NOT NULL,
                prompt_text TEXT,
                response_text LONGTEXT,
                input_tokens INT DEFAULT 0,
                output_tokens INT DEFAULT 0,
                total_tokens INT DEFAULT 0,
                success BOOLEAN NOT NULL DEFAULT FALSE,
                error_message TEXT,
                processing_time_ms INT DEFAULT 0,
                request_data JSON,
                response_data JSON,
                INDEX idx_date (date),
                INDEX idx_llm_name (llm_name),
                INDEX idx_timestamp (timestamp),
                INDEX idx_success (success)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """
            
            # Bảng quản lý calls theo ngày
            create_daily_calls_table = """
            CREATE TABLE IF NOT EXISTS daily_calls (
                id INT AUTO_INCREMENT PRIMARY KEY,
                date DATE NOT NULL,
                llm_name VARCHAR(255) NOT NULL,
                provider VARCHAR(100) NOT NULL,
                model VARCHAR(255) NOT NULL,
                max_calls INT NOT NULL DEFAULT 0,
                used_calls INT NOT NULL DEFAULT 0,
                remaining_calls INT NOT NULL DEFAULT 0,
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE KEY unique_daily_llm (date, llm_name),
                INDEX idx_date (date),
                INDEX idx_llm_name (llm_name)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """
            
            # Bảng lưu cấu hình LLM
            create_llm_configs_table = """
            CREATE TABLE IF NOT EXISTS llm_configs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL UNIQUE,
                provider VARCHAR(100) NOT NULL,
                model VARCHAR(255) NOT NULL,
                api_base_url TEXT,
                api_token TEXT,
                max_calls INT NOT NULL DEFAULT 0,
                max_input_tokens INT NOT NULL DEFAULT 3500,
                temperature FLOAT NOT NULL DEFAULT 0.0,
                enabled BOOLEAN NOT NULL DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_name (name),
                INDEX idx_provider (provider),
                INDEX idx_enabled (enabled)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """

            # Bảng quản lý collections
            create_collections_table = """
            CREATE TABLE IF NOT EXISTS collections (
                id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
                name VARCHAR(255) COLLATE utf8mb4_unicode_ci NOT NULL,
                display_name VARCHAR(255) COLLATE utf8mb4_unicode_ci NOT NULL,
                description TEXT COLLATE utf8mb4_unicode_ci,
                is_active TINYINT(1) NOT NULL DEFAULT '1',
                created_at TIMESTAMP NULL DEFAULT NULL,
                updated_at TIMESTAMP NULL DEFAULT NULL,
                PRIMARY KEY (id),
                UNIQUE KEY collections_name_unique (name)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """

            # Tạo các bảng
            cursor.execute(create_request_logs_table)
            cursor.execute(create_daily_calls_table)
            cursor.execute(create_llm_configs_table)
            cursor.execute(create_collections_table)
            
            logger.info("✅ Đã tạo tất cả bảng cần thiết")
            cursor.close()
            return True
            
        except Error as e:
            logger.error(f"❌ Lỗi tạo bảng: {e}")
            return False
    
    def init_database(self):
        """Khởi tạo database và các bảng"""
        if self.create_database() and self.connect():
            return self.create_tables()
        return False
    
    def log_request(self, log_data: Dict[str, Any]) -> bool:
        """
        Lưu log request vào database
        
        Args:
            log_data: Dictionary chứa thông tin log
                - llm_name: Tên LLM
                - provider: Provider (openai, gemini, ...)
                - model: Model name
                - prompt_text: Prompt gửi đi
                - response_text: Response nhận về
                - input_tokens: Số input tokens
                - output_tokens: Số output tokens
                - success: Thành công hay không
                - error_message: Lỗi nếu có
                - processing_time_ms: Thời gian xử lý (ms)
                - request_data: Raw request data
                - response_data: Raw response data
        
        Returns:
            bool: True nếu thành công
        """
        if not self.connection or not self.connection.is_connected():
            if not self.connect():
                return False
        
        try:
            cursor = self.connection.cursor()
            
            # Chuẩn bị dữ liệu
            now = datetime.now()
            today = date.today()
            
            insert_query = """
            INSERT INTO request_logs (
                timestamp, date, llm_name, provider, model, 
                prompt_text, response_text, input_tokens, output_tokens, total_tokens,
                success, error_message, processing_time_ms, request_data, response_data
            ) VALUES (
                %s, %s, %s, %s, %s, 
                %s, %s, %s, %s, %s,
                %s, %s, %s, %s, %s
            )
            """
            
            values = (
                now,
                today,
                log_data.get('llm_name', ''),
                log_data.get('provider', ''),
                log_data.get('model', ''),
                log_data.get('prompt_text', ''),
                log_data.get('response_text', ''),
                log_data.get('input_tokens', 0),
                log_data.get('output_tokens', 0),
                log_data.get('total_tokens', 0),
                log_data.get('success', False),
                log_data.get('error_message', ''),
                log_data.get('processing_time_ms', 0),
                json.dumps(log_data.get('request_data', {}), ensure_ascii=False),
                json.dumps(log_data.get('response_data', {}), ensure_ascii=False)
            )
            
            cursor.execute(insert_query, values)
            cursor.close()
            
            # Cập nhật daily_calls
            self.update_daily_call_count(
                llm_name=log_data.get('llm_name', ''),
                provider=log_data.get('provider', ''),
                model=log_data.get('model', ''),
                success=log_data.get('success', False)
            )
            
            logger.info(f"✅ Đã log request cho {log_data.get('llm_name', 'Unknown')}")
            return True
            
        except Error as e:
            logger.error(f"❌ Lỗi log request: {e}")
            return False
    
    def update_daily_call_count(self, llm_name: str, provider: str, model: str, success: bool = True) -> bool:
        """
        Cập nhật số lượng calls đã sử dụng trong ngày
        
        Args:
            llm_name: Tên LLM
            provider: Provider
            model: Model name
            success: Request có thành công không
        
        Returns:
            bool: True nếu thành công
        """
        if not self.connection or not self.connection.is_connected():
            if not self.connect():
                return False
        
        try:
            cursor = self.connection.cursor()
            today = date.today()
            
            # Kiểm tra xem đã có record cho ngày hôm nay chưa
            check_query = "SELECT used_calls, max_calls FROM daily_calls WHERE date = %s AND llm_name = %s"
            cursor.execute(check_query, (today, llm_name))
            result = cursor.fetchone()
            
            if result:
                # Cập nhật record hiện tại
                used_calls, max_calls = result
                new_used_calls = used_calls + (1 if success else 0)
                new_remaining_calls = max(0, max_calls - new_used_calls)
                
                update_query = """
                UPDATE daily_calls 
                SET used_calls = %s, remaining_calls = %s 
                WHERE date = %s AND llm_name = %s
                """
                cursor.execute(update_query, (new_used_calls, new_remaining_calls, today, llm_name))
            else:
                # Tạo record mới cho ngày hôm nay
                # Lấy max_calls từ cấu hình LLM
                max_calls = self.get_llm_max_calls(llm_name)
                used_calls = 1 if success else 0
                remaining_calls = max(0, max_calls - used_calls)
                
                insert_query = """
                INSERT INTO daily_calls (date, llm_name, provider, model, max_calls, used_calls, remaining_calls)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
                """
                cursor.execute(insert_query, (today, llm_name, provider, model, max_calls, used_calls, remaining_calls))
            
            cursor.close()
            logger.info(f"✅ Đã cập nhật daily calls cho {llm_name}")
            return True
            
        except Error as e:
            logger.error(f"❌ Lỗi cập nhật daily calls: {e}")
            return False
    
    def get_daily_remaining_calls(self, llm_name: str, date_obj: date = None) -> int:
        """
        Lấy số calls còn lại trong ngày cho LLM
        
        Args:
            llm_name: Tên LLM
            date_obj: Ngày cần check, nếu None sẽ dùng ngày hôm nay
        
        Returns:
            int: Số calls còn lại
        """
        if not self.connection or not self.connection.is_connected():
            if not self.connect():
                return 0
        
        if date_obj is None:
            date_obj = date.today()
        
        try:
            cursor = self.connection.cursor()
            
            query = "SELECT remaining_calls FROM daily_calls WHERE date = %s AND llm_name = %s"
            cursor.execute(query, (date_obj, llm_name))
            result = cursor.fetchone()
            
            cursor.close()
            
            if result:
                return result[0]
            else:
                # Nếu chưa có record cho ngày này, trả về max_calls
                return self.get_llm_max_calls(llm_name)
                
        except Error as e:
            logger.error(f"❌ Lỗi lấy remaining calls: {e}")
            return 0
    
    def reset_daily_calls(self, target_date: date = None) -> bool:
        """
        Reset calls cho ngày mới (được gọi vào đầu ngày)
        
        Args:
            target_date: Ngày cần reset, nếu None sẽ dùng ngày hôm nay
        
        Returns:
            bool: True nếu thành công
        """
        if not self.connection or not self.connection.is_connected():
            if not self.connect():
                return False
        
        if target_date is None:
            target_date = date.today()
        
        try:
            cursor = self.connection.cursor()
            
            # Lấy danh sách tất cả LLM configs
            llm_configs = self.get_all_llm_configs()
            
            for config in llm_configs:
                # Xóa hoặc cập nhật record cũ nếu có
                delete_query = "DELETE FROM daily_calls WHERE date = %s AND llm_name = %s"
                cursor.execute(delete_query, (target_date, config['name']))
                
                # Tạo record mới với calls được reset
                insert_query = """
                INSERT INTO daily_calls (date, llm_name, provider, model, max_calls, used_calls, remaining_calls)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
                """
                cursor.execute(insert_query, (
                    target_date,
                    config['name'],
                    config['provider'],
                    config['model'],
                    config['max_calls'],
                    0,  # used_calls = 0
                    config['max_calls']  # remaining_calls = max_calls
                ))
            
            cursor.close()
            logger.info(f"✅ Đã reset daily calls cho ngày {target_date}")
            return True
            
        except Error as e:
            logger.error(f"❌ Lỗi reset daily calls: {e}")
            return False
    
    def get_llm_max_calls(self, llm_name: str) -> int:
        """
        Lấy max_calls từ cấu hình LLM
        
        Args:
            llm_name: Tên LLM
        
        Returns:
            int: max_calls
        """
        if not self.connection or not self.connection.is_connected():
            if not self.connect():
                return 0
        
        try:
            cursor = self.connection.cursor()
            
            query = "SELECT max_calls FROM llm_configs WHERE name = %s"
            cursor.execute(query, (llm_name,))
            result = cursor.fetchone()
            
            cursor.close()
            
            return result[0] if result else 0
            
        except Error as e:
            logger.error(f"❌ Lỗi lấy max calls: {e}")
            return 0
    
    def sync_llm_configs(self, llm_configs: List[Dict[str, Any]]) -> bool:
        """
        Đồng bộ cấu hình LLM vào database
        
        Args:
            llm_configs: List các config LLM
        
        Returns:
            bool: True nếu thành công
        """
        if not self.connection or not self.connection.is_connected():
            if not self.connect():
                return False
        
        try:
            cursor = self.connection.cursor()
            
            for config in llm_configs:
                # Sử dụng ON DUPLICATE KEY UPDATE để upsert
                query = """
                INSERT INTO llm_configs (name, provider, model, api_base_url, api_token, max_calls, max_input_tokens, temperature, enabled)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                ON DUPLICATE KEY UPDATE
                    provider = VALUES(provider),
                    model = VALUES(model),
                    api_base_url = VALUES(api_base_url),
                    api_token = VALUES(api_token),
                    max_calls = VALUES(max_calls),
                    max_input_tokens = VALUES(max_input_tokens),
                    temperature = VALUES(temperature),
                    enabled = VALUES(enabled)
                """
                
                values = (
                    config.get('name', ''),
                    config.get('provider', ''),
                    config.get('model', ''),
                    config.get('api_base_url', ''),
                    config.get('api_token', ''),
                    config.get('max_calls', 0),
                    config.get('max_input_tokens', 3500),
                    config.get('temperature', 0.0),
                    config.get('enabled', True)
                )
                
                cursor.execute(query, values)
            
            cursor.close()
            logger.info(f"✅ Đã đồng bộ {len(llm_configs)} LLM configs")
            return True
            
        except Error as e:
            logger.error(f"❌ Lỗi đồng bộ LLM configs: {e}")
            return False
    
    def get_all_llm_configs(self) -> List[Dict[str, Any]]:
        """
        Lấy tất cả cấu hình LLM từ database
        
        Returns:
            List[Dict]: Danh sách config LLM
        """
        if not self.connection or not self.connection.is_connected():
            if not self.connect():
                return []
        
        try:
            cursor = self.connection.cursor(dictionary=True)
            
            # Lấy tất cả configs, không chỉ enabled ones
            query = "SELECT * FROM llm_configs WHERE enabled = 1 ORDER BY name"
            cursor.execute(query)
            results = cursor.fetchall()
            
            cursor.close()
            return results
            
        except Error as e:
            logger.error(f"❌ Lỗi lấy LLM configs: {e}")
            return []

    def get_collection_by_name(self, collection_name: str) -> Optional[Dict[str, Any]]:
        """
        Lấy thông tin collection theo tên

        Args:
            collection_name (str): Tên collection

        Returns:
            Optional[Dict]: Thông tin collection hoặc None nếu không tìm thấy
        """
        if not self.connection or not self.connection.is_connected():
            if not self.connect():
                return None

        try:
            cursor = self.connection.cursor(dictionary=True)

            query = "SELECT * FROM collections WHERE name = %s AND is_active = 1"
            cursor.execute(query, (collection_name,))
            result = cursor.fetchone()

            cursor.close()
            return result

        except Error as e:
            logger.error(f"❌ Lỗi lấy collection '{collection_name}': {e}")
            return None

    def get_all_collections(self) -> List[Dict[str, Any]]:
        """
        Lấy tất cả collections

        Returns:
            List[Dict]: Danh sách collections
        """
        if not self.connection or not self.connection.is_connected():
            if not self.connect():
                return []

        try:
            cursor = self.connection.cursor(dictionary=True)

            query = "SELECT * FROM collections WHERE is_active = 1 ORDER BY name"
            cursor.execute(query)
            results = cursor.fetchall()

            cursor.close()
            return results

        except Error as e:
            logger.error(f"❌ Lỗi lấy danh sách collections: {e}")
            return []

    def create_collection(self, name: str, display_name: str, description: str = None) -> bool:
        """
        Tạo collection mới

        Args:
            name (str): Tên collection (unique key)
            display_name (str): Tên hiển thị
            description (str): Mô tả collection

        Returns:
            bool: True nếu thành công
        """
        if not self.connection or not self.connection.is_connected():
            if not self.connect():
                return False

        try:
            cursor = self.connection.cursor()

            query = """
            INSERT INTO collections (name, display_name, description, is_active, created_at, updated_at)
            VALUES (%s, %s, %s, %s, NOW(), NOW())
            """
            cursor.execute(query, (name, display_name, description, True))
            self.connection.commit()

            cursor.close()
            logger.info(f"✅ Đã tạo collection '{name}' ({display_name})")
            return True

        except Error as e:
            logger.error(f"❌ Lỗi tạo collection '{name}': {e}")
            return False

    def collection_exists(self, collection_name: str) -> bool:
        """
        Kiểm tra collection có tồn tại không

        Args:
            collection_name (str): Tên collection

        Returns:
            bool: True nếu collection tồn tại và is_active
        """
        collection = self.get_collection_by_name(collection_name)
        return collection is not None
    
    def get_request_stats(self, start_date: date = None, end_date: date = None) -> Dict[str, Any]:
        """
        Lấy thống kê requests
        
        Args:
            start_date: Ngày bắt đầu
            end_date: Ngày kết thúc
        
        Returns:
            Dict: Thống kê
        """
        if not self.connection or not self.connection.is_connected():
            if not self.connect():
                return {}
        
        if start_date is None:
            start_date = date.today() - timedelta(days=7)
        if end_date is None:
            end_date = date.today()
        
        try:
            cursor = self.connection.cursor(dictionary=True)
            
            # Thống kê tổng quan
            stats_query = """
            SELECT 
                COUNT(*) as total_requests,
                SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successful_requests,
                SUM(CASE WHEN success = 0 THEN 1 ELSE 0 END) as failed_requests,
                SUM(input_tokens) as total_input_tokens,
                SUM(output_tokens) as total_output_tokens,
                AVG(processing_time_ms) as avg_processing_time_ms
            FROM request_logs 
            WHERE date BETWEEN %s AND %s
            """
            cursor.execute(stats_query, (start_date, end_date))
            general_stats = cursor.fetchone()
            
            # Thống kê theo LLM
            llm_stats_query = """
            SELECT 
                llm_name,
                provider,
                COUNT(*) as requests,
                SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successful,
                SUM(input_tokens) as input_tokens,
                SUM(output_tokens) as output_tokens
            FROM request_logs 
            WHERE date BETWEEN %s AND %s
            GROUP BY llm_name, provider
            ORDER BY requests DESC
            """
            cursor.execute(llm_stats_query, (start_date, end_date))
            llm_stats = cursor.fetchall()
            
            # Thống kê theo ngày
            daily_stats_query = """
            SELECT 
                date,
                COUNT(*) as requests,
                SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successful
            FROM request_logs 
            WHERE date BETWEEN %s AND %s
            GROUP BY date
            ORDER BY date DESC
            """
            cursor.execute(daily_stats_query, (start_date, end_date))
            daily_stats = cursor.fetchall()
            
            cursor.close()
            
            return {
                'period': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat()
                },
                'general': general_stats,
                'by_llm': llm_stats,
                'by_day': daily_stats
            }
            
        except Error as e:
            logger.error(f"❌ Lỗi lấy thống kê: {e}")
            return {}
    
    def cleanup_old_logs(self, days_to_keep: int = 30) -> bool:
        """
        Xóa log cũ để tiết kiệm dung lượng
        
        Args:
            days_to_keep: Số ngày log cần giữ lại
        
        Returns:
            bool: True nếu thành công
        """
        if not self.connection or not self.connection.is_connected():
            if not self.connect():
                return False
        
        try:
            cursor = self.connection.cursor()
            cutoff_date = date.today() - timedelta(days=days_to_keep)
            
            # Xóa request logs cũ
            delete_logs_query = "DELETE FROM request_logs WHERE date < %s"
            cursor.execute(delete_logs_query, (cutoff_date,))
            deleted_logs = cursor.rowcount
            
            # Xóa daily calls cũ
            delete_calls_query = "DELETE FROM daily_calls WHERE date < %s"
            cursor.execute(delete_calls_query, (cutoff_date,))
            deleted_calls = cursor.rowcount
            
            cursor.close()
            
            logger.info(f"✅ Đã xóa {deleted_logs} request logs và {deleted_calls} daily calls cũ hơn {days_to_keep} ngày")
            return True
            
        except Error as e:
            logger.error(f"❌ Lỗi cleanup logs: {e}")
            return False

# Context manager để tự động đóng kết nối
class DatabaseContext:
    """Context manager cho database operations"""
    
    def __init__(self, config: DatabaseConfig = None):
        self.db_manager = DatabaseManager(config)
    
    def __enter__(self):
        return self.db_manager
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.db_manager.disconnect()

# Utility functions
def create_database_config_from_env() -> DatabaseConfig:
    """Tạo database config từ environment variables"""
    from libs.common import get_env_var

    return DatabaseConfig(
        host=get_env_var('DB_HOST', 'localhost'),
        port=int(get_env_var('DB_PORT', '3306')),
        database=get_env_var('DB_NAME', 'llm_router'),
        user=get_env_var('DB_USER', 'root'),
        password=get_env_var('DB_PASSWORD', ''),
        charset=get_env_var('DB_CHARSET', 'utf8mb4')
    )
