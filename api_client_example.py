#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ví dụ client để gọi FastAPI Word Document Processor
"""

import requests
import json
import os

# URL của API
API_BASE_URL = "http://localhost:8000"

def test_api_health():
    """Kiểm tra trạng thái API"""
    print("=== KIỂM TRA TRẠNG THÁI API ===")
    
    try:
        response = requests.get(f"{API_BASE_URL}/health")
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"Lỗi khi kiểm tra API: {e}")
        return False

def process_docx_upload(file_path, max_tokens=None, verbose=False):
    """Upload và xử lý file Word"""
    print(f"\n=== UPLOAD VÀ XỬ LÝ FILE: {file_path} ===")
    
    if max_tokens is not None:
        print(f"⚠️  Warning: max_tokens parameter is deprecated. Using max_input_tokens from LLM config instead.")
    
    if not os.path.exists(file_path):
        print(f"File không tồn tại: {file_path}")
        return None
    
    try:
        # Chuẩn bị files và data
        files = {'file': open(file_path, 'rb')}
        data = {
            'max_tokens': max_tokens,  # DEPRECATED - will be ignored by server
            'verbose': verbose
        }
        
        # Gửi request
        response = requests.post(
            f"{API_BASE_URL}/process-docx",
            files=files,
            data=data
        )
        
        # Đóng file
        files['file'].close()
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"Success: {result['success']}")
            print(f"Message: {result['message']}")
            print(f"Number of results: {len(result['results']) if result['results'] else 0}")
            
            if result['file_info']:
                print(f"File info: {result['file_info']}")
            
            # Hiển thị kết quả chi tiết
            display_json_results(result)
            
            return result
        else:
            error = response.json()
            print(f"Error: {error}")
            return None
            
    except Exception as e:
        print(f"Lỗi khi gọi API: {e}")
        return None

def process_docx_from_path(file_path, max_tokens=7500, verbose=False):
    """Xử lý file Word từ đường dẫn"""
    print(f"\n=== XỬ LÝ FILE TỪ ĐƯỜNG DẪN: {file_path} ===")
    
    try:
        # Chuẩn bị data
        data = {
            'file_path': file_path,
            'max_tokens': max_tokens,
            'verbose': verbose
        }
        
        # Gửi request
        response = requests.post(
            f"{API_BASE_URL}/process-docx-path",
            data=data
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"Success: {result['success']}")
            print(f"Message: {result['message']}")
            print(f"Number of results: {len(result['results']) if result['results'] else 0}")
            
            if result['file_info']:
                print(f"File info: {result['file_info']}")
            
            # Hiển thị kết quả chi tiết
            display_json_results(result)
            
            return result
        else:
            error = response.json()
            print(f"Error: {error}")
            return None
            
    except Exception as e:
        print(f"Lỗi khi gọi API: {e}")
        return None

def parse_results(results):
    """Parse kết quả từ AI"""
    print(f"\n=== PARSE KẾT QUẢ ===")
    
    try:
        # Gửi request
        response = requests.post(
            f"{API_BASE_URL}/parse-results",
            json=results
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"Success: {result['success']}")
            print(f"Total chunks: {result['total_chunks']}")
            
            # In từng chunk đã parse
            for chunk_data in result['parsed_results']:
                print(f"\n--- Chunk {chunk_data['chunk_index']} ---")
                if chunk_data['parsed_data']:
                    print("Parsed JSON:")
                    for key, value in chunk_data['parsed_data'].items():
                        print(f"  {key}: {value}")
                else:
                    print("Không thể parse JSON:")
                    print(f"  Error: {chunk_data.get('parse_error', 'Unknown error')}")
                    print(f"  Raw result: {chunk_data['raw_result'][:200]}...")
            
            return result
        else:
            error = response.json()
            print(f"Error: {error}")
            return None
            
    except Exception as e:
        print(f"Lỗi khi parse kết quả: {e}")
        return None

def display_json_results(result):
    """Hiển thị kết quả JSON chi tiết"""
    if not result or not result.get('results'):
        return
    
    print(f"\n--- CHI TIẾT KẾT QUẢ ---")
    successful = 0
    failed = 0
    
    for chunk_result in result['results']:
        chunk_idx = chunk_result.get('chunk_index', 0)
        success = chunk_result.get('success', False)
        
        if success:
            successful += 1
            print(f"\nChunk {chunk_idx}: ✅ SUCCESS")
            data = chunk_result.get('data', {})
            for key, value in data.items():
                # Giới hạn độ dài hiển thị cho value
                if isinstance(value, str) and len(value) > 100:
                    display_value = value[:100] + "..."
                else:
                    display_value = value
                print(f"  {key}: {display_value}")
        else:
            failed += 1
            print(f"\nChunk {chunk_idx}: ❌ FAILED")
            print(f"  Error: {chunk_result.get('error', 'Unknown error')}")
            raw_text = chunk_result.get('raw_text', '')
            if raw_text:
                preview = raw_text[:100] + "..." if len(raw_text) > 100 else raw_text
                print(f"  Raw text preview: {preview}")
    
    print(f"\nTÓM TẮT: {successful} thành công, {failed} thất bại")

def main():
    """Function chính để test API"""
    print("DEMO SỬ DỤNG FastAPI Word Document Processor")
    print("=" * 60)
    
    # 1. Kiểm tra trạng thái API
    if not test_api_health():
        print("API không hoạt động. Vui lòng khởi động server trước.")
        return
    
    # 2. Test với file upload (nếu có file)
    test_files = ["1.docx", "2.docx", "3.docx"]
    
    for file_path in test_files:
        if os.path.exists(file_path):
            print(f"\nTìm thấy file: {file_path}")
            
            # Test upload
            result = process_docx_upload(file_path, max_tokens=5000, verbose=True)
            
            # Kết quả đã được hiển thị chi tiết trong function process_docx_upload
            
            break  # Chỉ test file đầu tiên tìm thấy
    else:
        print("\nKhông tìm thấy file test nào. Thử với đường dẫn...")
        
        # 3. Test với đường dẫn file (nếu có)
        for file_path in test_files:
            if os.path.exists(file_path):
                result = process_docx_from_path(file_path, max_tokens=5000, verbose=True)
                
                # Kết quả đã được hiển thị chi tiết trong function process_docx_from_path
                
                break
        else:
            print("Không có file nào để test. Vui lòng đặt file .docx trong thư mục hiện tại.")
    
    print(f"\n{'='*60}")
    print("HƯỚNG DẪN SỬ DỤNG:")
    print("1. Khởi động API: python main.py")
    print("2. Truy cập Swagger UI: http://localhost:8000/docs")
    print("3. Upload file .docx và xử lý")
    print("4. Kết quả trả về đã được format JSON tự động")

if __name__ == "__main__":
    main()
