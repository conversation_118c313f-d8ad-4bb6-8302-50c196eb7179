# Hướng dẫn sử dụng Gemini API

## Cài đặt

1. <PERSON><PERSON><PERSON> đặt thư viện cần thiết:
```bash
pip install langchain-google-genai
```

2. Hoặc cài đặt tất cả dependencies:
```bash
pip install -r requirements.txt
```

## Cấu hình

1. Tạo file `.env` từ `.env.example`:
```bash
cp .env.example .env
```

2. Thêm Gemini API key vào file `.env`:
```
GEMINI_API_KEY=your_gemini_api_key_here
```

3. Tùy chọn: <PERSON><PERSON><PERSON> hì<PERSON> danh sách models Gemini:
```
GEMINI_MODELS=gemini-1.5-pro,gemini-1.5-flash,gemini-1.0-pro
```

## Lấy Gemini API Key

1. Truy cập [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Đ<PERSON>ng nhập với tài khoản Google
3. Tạo API key mới
4. Copy API key và thêm vào file `.env`

## Sử dụng

### Sử dụng trong code Python:
```python
from ai import extract_sections_from_docx

# Sử dụng Gemini Pro
results = extract_sections_from_docx('input.docx', model='gemini-1.5-pro')

# Sử dụng Gemini Flash (nhanh hơn, rẻ hơn)
results = extract_sections_from_docx('input.docx', model='gemini-1.5-flash')
```

### Sử dụng từ command line:
```bash
# Liệt kê tất cả models có sẵn
python ai.py --list-models

# Sử dụng Gemini Pro
python ai.py input.docx gemini-1.5-pro

# Sử dụng Gemini Flash
python ai.py input.docx gemini-1.5-flash
```

## Models Gemini có sẵn

- **gemini-1.5-pro**: Model mạnh nhất, xử lý tốt các tác vụ phức tạp
- **gemini-1.5-flash**: Model nhanh, tối ưu cho tốc độ và chi phí
- **gemini-1.0-pro**: Model cũ hơn, vẫn hoạt động tốt cho nhiều tác vụ

## Lưu ý

- Gemini không hỗ trợ system messages, code sẽ tự động chuyển đổi
- Gemini có giới hạn rate limit khác với OpenAI
- Model gemini-1.5-pro có context window lớn hơn, phù hợp cho văn bản dài

## Troubleshooting

### Lỗi "langchain_google_genai not installed"
```bash
pip install langchain-google-genai
```

### Lỗi "GEMINI_API_KEY environment variable is required"
Kiểm tra file `.env` và đảm bảo đã thêm:
```
GEMINI_API_KEY=your_actual_api_key_here
```

### Lỗi API rate limit
Gemini có giới hạn requests per minute. Hãy đợi một chút trước khi thử lại.
