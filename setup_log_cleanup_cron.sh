#!/bin/bash
# Script để setup cron job cho log cleanup

# <PERSON><PERSON>u sắc cho output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔧 Setting up Log Cleanup Cron Job${NC}"
echo "=================================="

# Lấy đường dẫn hiện tại
CURRENT_DIR=$(pwd)
PYTHON_PATH=$(which python3)
SCRIPT_PATH="$CURRENT_DIR/cleanup_logs.py"

echo -e "${BLUE}📁 Current directory:${NC} $CURRENT_DIR"
echo -e "${BLUE}🐍 Python path:${NC} $PYTHON_PATH"
echo -e "${BLUE}📜 Script path:${NC} $SCRIPT_PATH"

# <PERSON><PERSON><PERSON> tra file script tồn tại
if [ ! -f "$SCRIPT_PATH" ]; then
    echo -e "${RED}❌ Error: cleanup_logs.py not found in current directory${NC}"
    exit 1
fi

# Kiểm tra python3
if [ ! -f "$PYTHON_PATH" ]; then
    echo -e "${RED}❌ Error: python3 not found${NC}"
    exit 1
fi

echo ""
echo -e "${YELLOW}⚙️  Cron Job Options:${NC}"
echo "1. Daily at 2:00 AM (recommended)"
echo "2. Daily at 3:00 AM"
echo "3. Weekly on Sunday at 2:00 AM"
echo "4. Custom schedule"
echo "5. View current cron jobs"
echo "6. Remove existing log cleanup cron job"

read -p "Choose an option (1-6): " choice

case $choice in
    1)
        CRON_SCHEDULE="0 2 * * *"
        DESCRIPTION="Daily at 2:00 AM"
        ;;
    2)
        CRON_SCHEDULE="0 3 * * *"
        DESCRIPTION="Daily at 3:00 AM"
        ;;
    3)
        CRON_SCHEDULE="0 2 * * 0"
        DESCRIPTION="Weekly on Sunday at 2:00 AM"
        ;;
    4)
        echo ""
        echo -e "${YELLOW}Enter custom cron schedule (format: minute hour day month weekday)${NC}"
        echo "Examples:"
        echo "  0 2 * * *     - Daily at 2:00 AM"
        echo "  30 1 * * *    - Daily at 1:30 AM"
        echo "  0 2 * * 0     - Weekly on Sunday at 2:00 AM"
        echo "  0 2 1 * *     - Monthly on 1st day at 2:00 AM"
        read -p "Cron schedule: " CRON_SCHEDULE
        DESCRIPTION="Custom: $CRON_SCHEDULE"
        ;;
    5)
        echo ""
        echo -e "${BLUE}📋 Current cron jobs:${NC}"
        crontab -l 2>/dev/null | grep -E "(cleanup_logs|log.*cleanup)" || echo "No log cleanup cron jobs found"
        exit 0
        ;;
    6)
        echo ""
        echo -e "${YELLOW}🗑️  Removing existing log cleanup cron jobs...${NC}"
        # Backup current crontab
        crontab -l 2>/dev/null > /tmp/crontab_backup_$(date +%Y%m%d_%H%M%S)
        # Remove log cleanup jobs
        crontab -l 2>/dev/null | grep -v "cleanup_logs" | crontab -
        echo -e "${GREEN}✅ Log cleanup cron jobs removed${NC}"
        exit 0
        ;;
    *)
        echo -e "${RED}❌ Invalid option${NC}"
        exit 1
        ;;
esac

echo ""
echo -e "${YELLOW}📝 Log Cleanup Configuration:${NC}"
echo "1. Keep logs for 30 days (default)"
echo "2. Keep logs for 7 days"
echo "3. Keep logs for 14 days"
echo "4. Keep logs for 60 days"
echo "5. Custom days"

read -p "Choose retention period (1-5): " retention_choice

case $retention_choice in
    1)
        DAYS_TO_KEEP=30
        ;;
    2)
        DAYS_TO_KEEP=7
        ;;
    3)
        DAYS_TO_KEEP=14
        ;;
    4)
        DAYS_TO_KEEP=60
        ;;
    5)
        read -p "Enter number of days to keep logs: " DAYS_TO_KEEP
        if ! [[ "$DAYS_TO_KEEP" =~ ^[0-9]+$ ]] || [ "$DAYS_TO_KEEP" -lt 1 ]; then
            echo -e "${RED}❌ Invalid number of days${NC}"
            exit 1
        fi
        ;;
    *)
        echo -e "${RED}❌ Invalid option${NC}"
        exit 1
        ;;
esac

# Tạo cron command
CRON_COMMAND="cd $CURRENT_DIR && $PYTHON_PATH cleanup_logs.py --days $DAYS_TO_KEEP --quiet"
CRON_LINE="$CRON_SCHEDULE $CRON_COMMAND # API Log Cleanup"

echo ""
echo -e "${BLUE}📋 Cron Job Summary:${NC}"
echo -e "${BLUE}Schedule:${NC} $DESCRIPTION"
echo -e "${BLUE}Retention:${NC} $DAYS_TO_KEEP days"
echo -e "${BLUE}Command:${NC} $CRON_COMMAND"
echo ""

read -p "Do you want to add this cron job? (y/N): " confirm

if [[ $confirm =~ ^[Yy]$ ]]; then
    # Backup current crontab
    BACKUP_FILE="/tmp/crontab_backup_$(date +%Y%m%d_%H%M%S)"
    crontab -l 2>/dev/null > "$BACKUP_FILE"
    echo -e "${BLUE}💾 Crontab backed up to: $BACKUP_FILE${NC}"
    
    # Add new cron job
    (crontab -l 2>/dev/null; echo "$CRON_LINE") | crontab -
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Cron job added successfully!${NC}"
        echo ""
        echo -e "${BLUE}📋 Current cron jobs:${NC}"
        crontab -l | grep -E "(cleanup_logs|log.*cleanup)"
        echo ""
        echo -e "${YELLOW}💡 Tips:${NC}"
        echo "- View all cron jobs: crontab -l"
        echo "- Edit cron jobs: crontab -e"
        echo "- Test cleanup manually: python3 cleanup_logs.py --dry-run"
        echo "- Check cron logs: tail -f /var/log/cron (or /var/log/syslog)"
    else
        echo -e "${RED}❌ Failed to add cron job${NC}"
        exit 1
    fi
else
    echo -e "${YELLOW}⚠️  Cron job not added${NC}"
fi

echo ""
echo -e "${GREEN}🎉 Setup completed!${NC}"
